<?php

/**
 * Class Service_Page_DeskV1_Student_CourseRecordMeta
 */
class Service_Page_DeskV1_Student_CourseRecordMeta {


    private $_objDsTrans;
    //学员所有课程退课+调课信息
    private $allCourseRefund;
    private $courseIdSet;
    //学生id
    private $customUid;
    //订单列表
    private $tradeList;
    //真人id
    private $scUid;
    //当前课程
    private $currentCourseId;
    //辅导老师uid
    private $assistantUid;

    //例子id
    private $leadsId;


    private $startServiceTime = 0;
    private $stopServiceTime  = 0;

    //meta 数据
    private $totalCourseCnt  = 0;
    private $totalCurrentNum = 0;
    private $allMoney        = 0;
    private $allAttendNum    = 0;
    private $allFinishNum    = 0;
    private $allPlayNum      = 0;
    private $currentLPCUid   = 0;


    public function __construct() {
        $this->_objDsTrans = new Service_Data_ScTrans();
    }

    /**
     * @param $arrInput
     * @return array
     * @throws Laxin_Util_Exception
     */
    public function execute($arrInput) {
        $customUid          = (int)$arrInput['studentUid'];
        $scUid              = (int)$arrInput['personUid'];
        $currentCourseId    = (int)$arrInput['courseId'];
        $leadsId            = (int)$arrInput['leadsId'];
        $this->assistantUid = (int)$arrInput['assistantUid'];

        if ($customUid <= 0 || $scUid <= 0 || $currentCourseId <= 0) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '参数错误');

        }
        if (empty($leadsId)) {
            $leadsId = AssistantDesk_Data_Student::getLeadsIdByStudentUidCourseId($customUid, $currentCourseId);
            if (empty($leadsId)) {
                throw new Common_Exception(Common_ExceptionCodes::DESK_PARAM_ERROR, '获取leadsId失败');
            }
            Bd_Log::notice('CourseRecordMeta,get_leadsId:' . $leadsId);

        }
        $this->customUid       = $customUid;
        $this->scUid           = $scUid;
        $this->currentCourseId = $currentCourseId;
        $this->leadsId         = $leadsId;

        $this->initData();

        return $this->formatMetaData();

    }

    private function formatMetaData() {
        return [
            'totalNum'        => $this->totalCourseCnt, // 购课总数
            'totalCurrentNum' => $this->totalCurrentNum, // 本期课程
            'allMoney'        => $this->allMoney, // 实付金额
            'allAttendNum'    => $this->allAttendNum, // 已开章节
            'allFinishNum'    => $this->allFinishNum, // 到课章节
            'allPlayNum'      => $this->allPlayNum, // 回放章节
        ];
    }

    private function initData() {
        $this->initAllTradeCourseList();
        $this->initCustomAllCourseRefund();
        $this->initServiceStartStopTime();
        $this->initLessonData();
        $this->initMetaData();
    }

    /**
     *订单信息和课程id
     */
    private function initAllTradeCourseList() {
        if (empty($this->customUid)) {
            return;
        }
        $this->tradeList = $this->getAllTradeCourseList($this->customUid);
        Bd_Log::notice('initAllTradeCourseList_tradeList' . json_encode($this->tradeList));
        $this->courseIdSet    = array_keys($this->tradeList);
        $this->totalCourseCnt = count($this->courseIdSet);

    }

    private function initServiceStartStopTime() {
        $scLeadsInfo = Api_Allocate_Api::getLeadsByIds([$this->leadsId]);
        if ($scLeadsInfo === false) {
            Bd_Log::warning("获取例子信息失败" . $this->leadsId);
            return;
        }
        if (!isset($scLeadsInfo[$this->leadsId])) {
            return;
        }
        $scLeadsInfo      = $scLeadsInfo[$this->leadsId];
        $startServiceTime = !empty($scLeadsInfo['scAllocTime']) ? $scLeadsInfo['scAllocTime'] : 0;

        $stopServiceTime        = Api_Tower::getExpireTimeByCourse($this->currentCourseId);
        $this->startServiceTime = $startServiceTime;
        $this->stopServiceTime  = $stopServiceTime;


    }


    private function initLessonData() {
        $lpcLeadsInfo = Assistant_Common_Service_DataService_Query_LeadsData::getIdlLpcLeadsDataByStudentUids(
            [$this->customUid],
            ['course_id', 'playback_num', 'finish_num']);
        if ($lpcLeadsInfo === false) {
            Bd_Log::warning('获取例子信息失败' . $this->customUid);
            return;
        }
        if (empty($lpcLeadsInfo)) {
            return;
        }
        $courseIds        = array_column($lpcLeadsInfo, 'course_id');
        $courseField      = ['courseId'];
        $lessonField      = ['lessonId', 'courseId', 'startTime'];
        $courseLessonInfo = Api_Dal::getCourseLessonInfoByCourseIds($courseIds, $courseField, $lessonField);
        if ($courseLessonInfo === false) {
            Bd_Log::warning('dal获取课程章节数据失败' . json_encode($courseIds));
            $courseLessonInfo = [];
        }
        $classOpen = []; // 开课章节
        $nowTime   = time();
        foreach ($courseLessonInfo as $lessons) {
            foreach ($lessons['lessonList'] as $lesson) {
                if ($nowTime >= $lesson['startTime']) {
                    $classOpen[$lesson['courseId']]++;
                } else {
                    $classOpen[$lesson['courseId']] = 0;
                }
            }
        }
        $classOpenNum = array_sum(array_values($classOpen));

        $allFinishNum   = 0;
        $allPlaybackNum = 0;
        if (!empty($lpcLeadsInfo)) {
            foreach ($lpcLeadsInfo as $item) {
                $allFinishNum   += $item['finish_num'];
                $allPlaybackNum += $item['playback_num'];
            }
        }
        $this->allFinishNum = $allFinishNum;
        $this->allPlayNum   = $allPlaybackNum;
        $this->allAttendNum = $classOpenNum;

    }

    private function initMetaData() {

        $totalCurrentNum = 0;
        $allMoney        = 0;
        foreach ($this->courseIdSet as $v) {
            $courseId = $v;
            $refund   = !empty($this->allCourseRefund[$courseId]['refund']) ? (int)$this->allCourseRefund[$courseId]['refund'] : 0;
            if ($refund == 0 && $this->startServiceTime <= $this->tradeList[$courseId]['tradeTime'] && $this->stopServiceTime >= $this->tradeList[$courseId]['tradeTime']) {
                $totalCurrentNum += 1;
            }
            $allMoney += (int)$this->tradeList[$courseId]['tradeFee'];

        }
        $this->totalCurrentNum = $totalCurrentNum;
        $this->allMoney        = $allMoney;


    }


    /**
     *获取学员所有课程退课+调课信息
     */
    private function initCustomAllCourseRefund() {
        if (empty($this->scUid) || empty($this->customUid) || empty($this->courseIdSet)) {
            return;
        }
        $this->allCourseRefund = $this->getCustomAllCourseRefund($this->scUid, $this->customUid, $this->courseIdSet);
    }

    /**
     * @param int $customUid
     * @return array|false
     */
    private
    function getAllTradeCourseList(int $customUid) {

        // $sourceArr = AssistantDesk_Data_Course::getAllSource();

        $tradeList = Api_Billing::orderList($customUid, []);
        Bd_Log::notice('getAllTradeCourseList_tradeList' . json_encode($tradeList));
        if ($tradeList === false) {
            Bd_Log::warning("获取订单信息错误");
            $this->tradeList = [];
            return;

        }
        $retTradeList = [];
        if (!empty($tradeList)) {
            foreach ($tradeList as $trade) {
                foreach ($trade['subList'] as $subTrade) {
                    //跳过非已支付的例子
                    if (!in_array($subTrade['status'], [Zb_Const_Trade::TRADE_STATUS_PAID, Zb_Const_Trade::TRADE_STATUS_REFUNDPART, Zb_Const_Trade::TRADE_STATUS_REFUNDING, Zb_Const_Trade::TRADE_STATUS_REFUND_PAUSE])) {
                        continue;
                    }
                    // 跳过课程id为0的
                    if (empty($subTrade['courseId'])) {
                        continue;
                    }
                    $retTradeList[$subTrade['courseId']] = [
                        'subTradeId' => $subTrade['subTradeId'],
                        'tradeId'    => $subTrade['tradeId'],
                        'skuId'      => $subTrade['skuId'],
                        'courseId'   => $subTrade['courseId'],
                        'itemTag'    => $subTrade['itemTag'],
                        'tradeFee'   => $subTrade['tradeFee'],
                        'tradeTime'  => $subTrade['tradeTime'],
                        'status'     => $subTrade['status'],

                    ];
                }
            }
        }

        return $retTradeList;
    }


    private
    function getBatchCourseInfo($courseIdList) {
        $courseFieldList = [
            "courseId",             // 课程ID
            "courseName",           // 课程名称
            "courseType",           // 课程类型 //type
            "learnSeason",          // 学季ID
            "year",                 // 学年
            "firstLessonTime",      // 第一个章节开课时间
            "lastLessonStopTime",   // 最后一个章节结束时间
            "status",               // 状态
            "isInner",              // 是否内部课
            "startTime",            // 开始时间
            "stopTime",             // 结束时间
            "createTime",           // 创建时间
            "onlineFormatTime",     // 上课时间格式化
            "hasMaterial",          // 是否有教材
            "mainGradeId",          // 主年级ID
            "mainSubjectId",        // 主学科
            'playType',             // 区分1为demo课程
            'hasSCWXGroupService',  // 说明：0 无sc微信群服务，1 有sc微信群服务
            'shortTraining',        //
            'newCourseType',
            'serviceInfo'
        ];

        $lessonFieldList = [
            "lessonId",             // 章节ID
            "startTime",            // 开始时间
        ];

        Hk_Util_Log::start("timer_getBatchCourseInfo");
        $courseList = Api_Dal::getCourseLessonInfoByCourseIds($courseIdList, $courseFieldList, $lessonFieldList);
        Hk_Util_Log::stop("timer_getBatchCourseInfo");

        if ($courseList === false) {
            Bd_Log::warning('dal_getCourseLessonInfoByCourseIds_err:' . json_encode($courseIdList));
            return false;
        }


        return $courseList;
    }


    private
    function getCustomAllCourseRefund($scUid, $customUid, $courseIds) {
        if (0 >= $scUid || 0 >= $customUid || empty($courseIds) || !is_array($courseIds)) {
            return [];
        }

        $ret = $this->_objDsTrans->getTransDataBySc($courseIds, $scUid, $customUid);
        if (empty($ret)) {
            return [];
        }
        return array_column($ret, null, 'courseId');
    }


}
