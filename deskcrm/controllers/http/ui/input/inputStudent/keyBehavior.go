package inputStudent

import (
	"deskcrm/components"

	"github.com/gin-gonic/gin"
)

// KeyBehaviorParam 关键行为参数
type KeyBehaviorParam struct {
	StudentUid int `json:"studentUid" form:"studentUid" binding:"required"` // 学生UID
	CourseId   int `json:"courseId" form:"courseId" binding:"required"`     // 课程ID
	LeadsId    int `json:"leadsId" form:"leadsId"`                          // 线索ID，可选
}

// Validate 参数验证
func (p *KeyBehaviorParam) Validate(ctx *gin.Context) error {
	if p.StudentUid <= 0 || p.CourseId <= 0 {
		return components.ErrorParamInvalid
	}
	return nil
}
