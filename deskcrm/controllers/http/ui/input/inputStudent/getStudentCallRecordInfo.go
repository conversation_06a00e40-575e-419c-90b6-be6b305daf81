package inputStudent

import (
	"deskcrm/components"
	"deskcrm/middleware"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetStudentCallRecordInfoParam 获取学生通话记录信息参数
type GetStudentCallRecordInfoParam struct {
	StudentUid    int64 `json:"studentUid" form:"studentUid" binding:"required"` // 学生UID
	Type          int   `json:"type" form:"type"`                                // 类型
	CourseId      int64 `json:"courseId" form:"courseId"`                        // 课程ID
	BacktraceTime int   `json:"backtraceTime" form:"backtraceTime"`              // 追溯时间范围，单位：s
	IsApi         int   `json:"isApi" form:"isApi"`                              // 是否为API调用

	// 从用户信息中获取
	PersonUid    int64 `json:"personUid" form:"personUid"`       // 人员UID
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"` // 助教UID
}

// Validate 参数验证
func (p *GetStudentCallRecordInfoParam) Validate(ctx *gin.Context) error {
	// 获取登录用户信息
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		zlog.Warnf(ctx, "GetStudentCallRecordInfo get user info failed, err: %v", err)
		return components.ErrorUserNotLogin
	}

	// 获取设备信息
	deviceInfo, err := middleware.GetSelectDeviceInfo(ctx)
	if err != nil {
		zlog.Warnf(ctx, "GetStudentCallRecordInfo get device info failed, err: %v", err)
		return components.ErrorUserNotLogin
	}

	// 设置用户信息
	p.PersonUid = int64(userInfo.UserId)
	p.AssistantUid = deviceInfo.DeviceUid

	// 参数合法性检查
	if p.StudentUid <= 0 {
		return components.InvalidParam("studentUid is required and must be greater than 0")
	}

	return nil
}
