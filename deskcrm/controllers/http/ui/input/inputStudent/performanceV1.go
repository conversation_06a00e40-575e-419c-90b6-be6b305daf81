package inputStudent

import (
	"deskcrm/components"
	"deskcrm/middleware"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// PerformanceV1Param 学生课程表现数据获取接口参数
// 对应 PHP Service_Page_DeskV1_Student_PerformanceV1 的输入参数
type PerformanceV1Param struct {
	StudentUid   int64  `form:"studentUid" json:"studentUid" binding:"required"` // 学生用户ID
	PersonUid    int64  `form:"personUid" json:"personUid" `                     // 真人ID
	CourseId     int64  `form:"courseId" json:"courseId" binding:"required"`     // 课程ID
	LeadsId      int64  `form:"leadsId" json:"leadsId"`                          // 线索ID (可选，系统自动获取)
	Tab          string `form:"tab" json:"tab"`                                  // 数据标签页类型，默认核心数据
	AssistantUid int64  `form:"assistantUid" json:"assistantUid"`                // 辅导老师ID (可选)
	IsExport     bool   `form:"isExport" json:"isExport"`                        // 是否导出数据
}

// Validate 参数验证方法
// 实现与 PHP 版本相同的参数验证逻辑
func (p *PerformanceV1Param) Validate(ctx *gin.Context) error {
	// 核心参数验证
	if p.StudentUid <= 0 || p.CourseId <= 0 {
		return components.ErrorParamInvalid
	}

	// 默认值设置
	if p.Tab == "" {
		p.Tab = "core_data"
	}

	// 获取用户信息
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		zlog.Errorf(ctx, "PerformanceV1 获取登录用户信息失败, err: %+v", err)
		return components.ErrorSystemError
	}

	p.PersonUid = int64(userInfo.UserId)
	p.AssistantUid = userInfo.SelectedBusinessUid

	return nil
}
