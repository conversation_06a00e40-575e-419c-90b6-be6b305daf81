package inputStudent

import (
	"deskcrm/components"
	"deskcrm/middleware"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

// CourseRecordMetaParam 课程记录元数据请求参数
type CourseRecordMetaParam struct {
	StudentUid   int64 `json:"studentUid" form:"studentUid" binding:"required"` // 学生UID
	CourseId     int64 `json:"courseId" form:"courseId" binding:"required"`     // 课程ID
	LeadsId      int64 `json:"leadsId" form:"leadsId"`                          // 线索ID，可选
	PersonUid    int64 `json:"personUid" form:"personUid"`
	AssistantUid int64 `json:"assistantUid" form:"assistantUid"`
}

// Validate 参数验证
func (p *CourseRecordMetaParam) Validate(ctx *gin.Context) error {
	if p.StudentUid <= 0 || p.CourseId <= 0 {
		return components.ErrorParamInvalid
	}

	loginUserInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.ErrorUserNotLogin)
		return components.ErrorParamInvalid
	}
	p.PersonUid = int64(loginUserInfo.UserId)
	p.AssistantUid = loginUserInfo.SelectedBusinessUid

	return nil
}
