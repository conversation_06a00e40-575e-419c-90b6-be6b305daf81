package ui

import (
	"deskcrm/components"
	"deskcrm/service/ui"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

type userController struct{}

var UserController = userController{}

// GetUserInfo 获取用户信息接口
func (r userController) GetUserInfo(ctx *gin.Context) {
	// 调用服务层
	rsp, err := ui.UserService.GetUserInfo(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, rsp)
}
