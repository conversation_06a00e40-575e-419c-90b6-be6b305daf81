package ui

import (
	"deskcrm/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterHandlers crm ui 接口调用入口
func RegisterHandlers(rg *gin.RouterGroup) {
	ui := rg.Group("/ui/")
	// ------------------------------------------------ui router-------------------------------------------------------
	ui.Use(middleware.AuthCheck())
	crmRouter := ui.Group("/", middleware.SelectBusiness)
	crmNotCheckSelectBusinessRouter := ui.Group("/")

	courseGroup := crmRouter.Group("/course/")
	{
		courseGroup.POST("courselistandcardbyyear", CourseController.CourseListAndCardByYear)
		courseGroup.GET("courselistandcardbyyear", CourseController.CourseListAndCardByYear)

		courseGroup.POST("getLessonplayinfolist", CourseController.GetLessonPlayInfoList)
		courseGroup.GET("getLessonplayinfolist", CourseController.GetLessonPlayInfoList)
		courseGroup.GET("getsipinfo", CourseController.GetSipInfo)
	}

	// 用户相关路由
	userGroup := crmRouter.Group("/user/")
	{
		userGroup.POST("userinfo", UserController.GetUserInfo)
	}

	// 维系详情相关路由
	studentGroup := crmRouter.Group("/student/")
	{
		studentGroup.Any("getcoursetimetableday", StudentController.GetCourseTimeTableDay)
		studentGroup.Any("getcoursetimetableweek", StudentController.GetCourseTimeTableWeek)
		studentGroup.Any("getstudentorderlist", StudentController.GetStudentOrderList)
		studentGroup.Any("interviewreferlpc", StudentController.InterviewReferLpc)
		studentGroup.Any("interviewrecord", StudentController.GetInterviewRecord)
		studentGroup.Any("interviewrecordv2", StudentController.GetInterviewRecordV2)
		studentGroup.Any("detailconfig", StudentDetailConfigController.GetDetailConfig)
		studentGroup.Any("getwxbindinfo", StudentController.GetWxBindInfo)
		studentGroup.Any("keybehavior", StudentController.GetKeyBehavior)
		studentGroup.GET("/tag/tag/getstudentbind", StudentController.GetStudentBind)
		studentGroup.GET("/tag/template/getactivewithbinddata", StudentController.GetActiveWithBindData)
		studentGroup.GET("/customfield/getoptions", StudentController.GetCustomFieldOptions)
		studentGroup.GET("/student/studentcallinfo", StudentController.GetStudentCallInfo)
		studentGroup.Any("/sop/getstudentcallrecordinfo", StudentController.GetStudentCallRecordInfo)
		studentGroup.GET("/sop/getcontactflagoption", StudentController.GetContactFlagOption)
		studentGroup.GET("/sop/getcalltypelist", StudentController.GetCallTypeList)
		studentGroup.GET("/user/commongray", StudentController.CommonGray)
		studentGroup.GET("/studentdelaminationdifferencelistv1", StudentController.StudentDelaminationDifferenceListV1)
		studentGroup.Any("getstudentdetailpageoption", StudentController.GetDetailPageOption)
		studentGroup.Any("courserecorddefaultoption", StudentController.GetCourseRecordDefaultOption)
		studentGroup.Any("courserecordmeta", StudentController.GetCourseRecordMeta)
	}

	// 维系详情配置
	keepDetailConfigGroup := crmRouter.Group("/keepdetailconfig/")
	{
		keepDetailConfigGroup.GET("getschemabycourseid", KeepDetailConfigController.GetSchemaByCourseId)
	}

	taskGroup := crmRouter.Group("/task/")
	taskForPredataGroup := crmNotCheckSelectBusinessRouter.Group("/task/")
	{
		taskGroup.POST("collection", ArkController.Collection)
		taskGroup.GET("collection", ArkController.Collection)

		taskGroup.POST("predatacollection", ArkController.PreDataCollection)
		taskGroup.GET("predatacollection", ArkController.PreDataCollection)

		taskGroup.GET("getpagingconfig", MercuryController.GetPagingConfig)

		taskGroup.GET("lessoninfo", LessonController.GetLessonList)
		taskForPredataGroup.GET("predatalessoninfo", LessonController.GetLessonList)
	}

	filterGroup := crmRouter.Group("/filter/")
	{
		filterGroup.POST("getfilterconfig", ArkController.GetFilterConfig)
		filterGroup.GET("getfilterconfig", ArkController.GetFilterConfig)

		filterGroup.POST("studentlist", StudentController.GetStudentList)
	}

	arkGroup := crmRouter.Group("/ark/")
	{
		arkGroup.GET("isshownewphonecell", PhoneController.IsShowNewPhoneCall)

		arkGroup.GET("getfieldmaptree", ArkController.GetFieldMapTree)

		arkGroup.POST("gettemplate", ArkController.GetTemplate)

		arkGroup.POST("addrule", ArkController.AddArkRule)
		arkGroup.POST("delrule", ArkController.DelArkRule)
		arkGroup.POST("ruledetail", ArkController.RuleDetail)
		arkGroup.POST("rulelist", ArkController.RuleList)

	}

	// 自定义标签
	customTagGroup := crmRouter.Group("/customtag/")
	{
		customTagGroup.POST("batchaddtag", CustomTagController.BatchAddTag)
		customTagGroup.POST("batchdeltag", CustomTagController.BatchDelTag)
		customTagGroup.POST("edittag", CustomTagController.EditTag)
		customTagGroup.GET("getcustomtag", CustomTagController.GetCustomTag)
		customTagGroup.GET("getcustomtaglist", CustomTagController.GetCustomTagList)
	}

	// ------------------------------------------------api router-------------------------------------------------------
	apiRouter := rg.Group("/api")
	courseApiGroup := apiRouter.Group("/course/")
	{
		courseApiGroup.POST("courselistandcardbyyear", CourseController.CourseListAndCardByYearAPI)
		courseApiGroup.POST("getLessonplayinfolist", CourseController.GetLessonPlayInfoList)
		courseApiGroup.GET("allowautocallandmessage", CourseController.AllowAutoCallAndMessage)
	}

	apiFilterGroup := apiRouter.Group("/filter/")
	{
		apiFilterGroup.POST("getfilterconfig", ArkController.GetFilterConfigAPI)
		apiFilterGroup.GET("getfilterconfig", ArkController.GetFilterConfigAPI)
		apiFilterGroup.POST("studentlist", StudentController.GetStudentListAPI)
	}

	apiTaskGroup := apiRouter.Group("/task/")
	{
		apiTaskGroup.POST("lessoninfo", LessonController.GetLessonListAPI)
	}

	toolGroup := apiRouter.Group("/tool/")
	{
		toolGroup.GET("startTask", ToolController.StartUpdateHandler)
		toolGroup.GET("stopTask", ToolController.StopUpdateHandler)
	}
}
