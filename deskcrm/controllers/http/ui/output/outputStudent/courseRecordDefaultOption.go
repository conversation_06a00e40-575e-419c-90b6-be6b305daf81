package outputStudent

// CourseRecordDefaultOptionResp 获取课程记录默认选项响应
type CourseRecordDefaultOptionResp struct {
	Season            int64  `json:"season"`            // 学季
	NewCourseType     int64  `json:"newCourseType"`     // 新课程类型
	IsRefund          int    `json:"isRefund"`          // 是否退款状态
	Year              int64  `json:"year"`              // 年份
	CourseServiceType string `json:"courseServiceType"` // 课程服务类型
}
