package outputStudent

// CourseRecordMetaResponse 课程记录元数据响应
// 对应PHP的Service_Page_DeskV1_Student_CourseRecordMeta接口响应
type CourseRecordMetaResponse struct {
	TotalNum        int   `json:"totalNum"`        // 购课总数（对应PHP的totalNum）
	TotalCurrentNum int   `json:"totalCurrentNum"` // 本期课程（对应PHP的totalCurrentNum）
	AllMoney        int64 `json:"allMoney"`        // 实付金额（对应PHP的allMoney）
	AllAttendNum    int   `json:"allAttendNum"`    // 已开章节（对应PHP的allAttendNum）
	AllFinishNum    int   `json:"allFinishNum"`    // 到课章节（对应PHP的allFinishNum）
	AllPlayNum      int   `json:"allPlayNum"`      // 回放章节（对应PHP的allPlayNum）
}
