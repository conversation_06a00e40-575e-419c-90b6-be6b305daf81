package outputStudent

// GetStudentCallRecordInfoResp 获取学生通话记录信息响应
type GetStudentCallRecordInfoResp struct {
	CallList       []CallInfo    `json:"callList"`       // 可拨打电话列表
	CallRecordList []CallRecord  `json:"callRecordList"` // 通话记录列表
	CallCountInfo  CallCountInfo `json:"callCountInfo"`  // 通话统计信息
}

// CallInfo 电话信息
type CallInfo struct {
	Name      string `json:"name"`      // 电话名称（如：学生电话、监督人电话等）
	Phone     string `json:"phone"`     // 电话号码（脱敏后）
	Md5Phone  string `json:"md5Phone"`  // 电话号码MD5
	City      string `json:"city"`      // 城市
	CityLevel string `json:"cityLevel"` // 城市级别
}

// CallRecord 通话记录
type CallRecord struct {
	Name           string `json:"name"`           // 拨打人姓名
	StartTime      string `json:"startTime"`      // 开始时间
	Duration       int    `json:"duration"`       // 通话时长（秒）
	CallResult     string `json:"callResult"`     // 通话结果
	CallResultType int    `json:"callResultType"` // 通话结果类型（1：接通，0：未接通）
	CallId         string `json:"callId"`         // 通话ID
	CallMode       int    `json:"callMode"`       // 通话模式
	FromPhone      string `json:"fromPhone"`      // 主叫号码（脱敏后）
	SourceTypeName string `json:"sourceTypeName"` // 来源类型名称
	SourceType     int    `json:"sourceType"`     // 来源类型
	SortTime       int64  `json:"sortTime"`       // 排序时间戳
}

// CallCountInfo 通话统计信息
type CallCountInfo struct {
	TotalNum    int     `json:"totalNum"`    // 总通话次数
	SuccessNum  int     `json:"successNum"`  // 成功通话次数
	SuccessRate float64 `json:"successRate"` // 成功率
}
