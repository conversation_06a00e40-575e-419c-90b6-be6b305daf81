package outputUser

// UserInfoResp 用户信息响应结构体
type UserInfoResp struct {
	AssistantUid   int64         `json:"assistantUid"`   // 助教UID
	AssistantPhone string        `json:"assistantPhone"` // 助教电话
	Phone          string        `json:"phone"`          // 用户电话
	GradeGroup     int           `json:"gradeGroup"`     // 年级组
	PersonUid      int64         `json:"personUid"`      // 真人UID
	PersonPhone    string        `json:"personPhone"`    // 真人电话
	Uname          string        `json:"uname"`          // 用户名
	Nickname       string        `json:"nickname"`       // 昵称
	SeasonYear     int           `json:"seasonYear"`     // 学年
	LearnSeasonId  int           `json:"learnSeasonId"`  // 学季ID
	Avatar         string        `json:"avatar"`         // 头像
	MenuDigit      string        `json:"menuDigit"`      // 菜单标识
	Watermark      WatermarkInfo `json:"watermark"`      // 水印信息
	HasSmallFlow   int           `json:"hasSmallFlow"`   // 小流量标识
	LoginType      int           `json:"loginType"`      // 登录类型（1-passport，2-IPS）
	LogoutUrl      string        `json:"logoutUrl"`      // 登出URL
	AppId          int           `json:"appId"`          // 微信AppId
}

// WatermarkInfo 水印信息结构体
type WatermarkInfo struct {
	CurrentDate string `json:"currentDate"` // 当前日期（格式：YmdHi去掉前两位）
	UserNumber  string `json:"userNumber"`  // 用户号码（电话号码后6位）
	UserName    string `json:"userName"`    // 用户名称（昵称或用户名）
}
