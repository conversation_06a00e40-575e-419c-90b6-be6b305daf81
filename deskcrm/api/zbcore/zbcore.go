package zbcore

import (
	"bytes"
	"deskcrm/api/moat"
	"deskcrm/conf"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const Service = "zbcore"
const ServiceUri = "/zbcore/api/api"

const (
	Success              = 0 // 请求成功
	NetworkError         = 610001
	ApiInvalidError      = 610002
	ParamError           = 610003
	ResError             = 610004
	FieldError           = 610005
	QueryError           = 610006
	CourseUnexistError   = 630001
	SkuUnexistError      = 640001
	SubtradeUnexistError = 640002
	PretradeUnexistError = 640003
	TradeUnexistError    = 640004
	TradeStatusExp       = 640005
)

var _errNoMsgMap = map[int]string{
	Success:              "成功",
	NetworkError:         "网络错误",
	ApiInvalidError:      "接口不存在",
	ParamError:           "参数错误",
	ResError:             "返回数据格式错误",
	FieldError:           "请求数据字段不支持",
	QueryError:           "查询失败",
	CourseUnexistError:   "课程不存在",
	SkuUnexistError:      "SKU不存在",
	SubtradeUnexistError: "子订单不存在",
	PretradeUnexistError: "预约子订单不存在",
	TradeUnexistError:    "主订单不存在",
	TradeStatusExp:       "数据状态异常",
}

// 生成 Header 信息
// uriPath    请求路径
// module     请求模块
// entity     请求实体
// api        请求方法
func GetHeaders(uriPath string, module string, entity string, api string, isNeedAuth bool, app string) map[string]string {
	now := time.Now()
	uniQid := fmt.Sprintf("rpc%d", now.Nanosecond()/1000)
	requestId := fmt.Sprintf("%s_%d", uniQid, now.Unix())

	query := map[string]string{
		"requestId": requestId,
		"token":     "",
		"caller":    app,
		"tm":        strconv.FormatInt(now.Unix(), 10),
	}
	if len(module) > 0 {
		query["module"] = module
	}

	if len(entity) > 0 {
		query["entity"] = entity
	}
	if len(api) > 0 {
		query["api"] = api
	}

	// 传输过程中，+ 会被自动解析为空格。使用 urlencode 避免该问题，+ => %2B ; 空格 => +
	arrHeader := map[string]string{
		"XRequestApp": app,
		"querystring": buildQueryString(query),
		"pathinfo":    uriPath,
	}

	return arrHeader
}

func buildQueryString(data map[string]string) string {
	buf := bytes.NewBufferString("")
	for k, v := range data {
		if buf.Len() > 0 {
			buf.WriteByte('&')
		}
		buf.WriteString(k)
		buf.WriteByte('=')
		buf.WriteString(url.QueryEscape(v))
	}

	return buf.String()
}

func PostDau(ctx *gin.Context, input map[string]interface{}, header map[string]string, output interface{}) (*BzrResponse, error) {
	return call(ctx, "POST", input, header, output, conf.API.ZbCoreDau)
}

func PostDal(ctx *gin.Context, input map[string]interface{}, header map[string]string, output interface{}) (*BzrResponse, error) {
	return call(ctx, "POST", input, header, output, conf.API.ZbCoreDal)
}

func PostDat(ctx *gin.Context, input map[string]interface{}, header map[string]string, output interface{}) (*BzrResponse, error) {
	return call(ctx, "POST", input, header, output, conf.API.ZbCoreDat)
}

func PostDas(ctx *gin.Context, input map[string]interface{}, header map[string]string, output interface{}) (*BzrResponse, error) {
	return call(ctx, "POST", input, header, output, conf.API.ZbCoreDas)
}

func PostDar(ctx *gin.Context, input map[string]interface{}, header map[string]string, output interface{}) (*BzrResponse, error) {
	return callWithMoat(ctx, "POST", input, header, output, conf.API.ZbCoreDar)
}

func PostExamCore(ctx *gin.Context, input map[string]interface{}, header map[string]string, output interface{}) (*BzrResponse, error) {
	return call(ctx, "POST", input, header, output, conf.API.ExamCore)
}

// 调用rpc service
// 已经记录调用日志，请不要在前端再次记录rpc相关调用日志
// service        后端服务名称
// method         后端服务方法
// input          服务参数列表
// header         向后端通过httpHeader透传的数据
func call(ctx *gin.Context, method string, input map[string]interface{}, header map[string]string, output interface{}, client *base.ApiClient) (*BzrResponse, error) {
	arg := map[string]string{
		"type":    "rpc",
		"srvName": Service,
		"method":  method,
		"input":   "",
		"header":  "",
	}

	jsonBytes, err := json.Marshal(input)
	if err != nil {
		return nil, err
	}
	arg["input"] = string(jsonBytes)

	if len(header) > 0 {
		jsonBytes, err := json.Marshal(header)
		if err != nil {
			return nil, err
		}

		arg["header"] = string(jsonBytes)
	}

	rawByte, err := json.Marshal(input)
	if err != nil {
		return nil, err
	}

	input["raw"] = string(rawByte)

	opts := base.HttpRequestOptions{
		RequestBody: input,
		Encode:      base.EncodeForm,
		Headers:     header,
	}

	var apiRespStruct BzrResponse
	apiUrl := fmt.Sprintf("%s?%s", ServiceUri, header["querystring"])
	resp, err := client.HttpPost(ctx, apiUrl, opts)
	zlog.Infof(ctx, "zbcore, arg:%+v,", arg)
	if err != nil {
		zlog.Warnf(ctx, "ral request failed, %s", err)

		// 需要封装一下错误输出
		return nil, apiError(NetworkError, "")
	}

	// 如果查询的课程或章节不存在，会返回 lessonId:[], 这种形式，解码会报错。需要兼容一下
	// 2021-03-03: 不在这里兼容, 在调用的地方兼容, 参见 dal 的实现

	if err = DecodeResponse(ctx, resp, &apiRespStruct, output); err != nil {
		zlog.Warnf(ctx, "ral decode failed, output:(%T), %s", output, err)
		return nil, err
	}

	return &apiRespStruct, nil
}

func callWithMoat(ctx *gin.Context, method string, input map[string]interface{}, header map[string]string, output interface{}, client *base.ApiClient) (*BzrResponse, error) {
	arg := map[string]string{
		"type":    "rpc",
		"srvName": Service,
		"method":  method,
		"input":   "",
		"header":  "",
	}

	jsonBytes, err := json.Marshal(input)
	if err != nil {
		return nil, err
	}
	arg["input"] = string(jsonBytes)

	if len(header) > 0 {
		jsonBytes, err := json.Marshal(header)
		if err != nil {
			return nil, err
		}

		arg["header"] = string(jsonBytes)
	}

	rawByte, err := json.Marshal(input)
	if err != nil {
		return nil, err
	}

	input["raw"] = string(rawByte)
	input["appKey"] = moat.AppKey
	input["appSecret"] = moat.AppSecret

	moatParams, signErr := moat.SignParams(ctx, input)
	if signErr != nil {
		return nil, signErr
	}

	opts := base.HttpRequestOptions{
		RequestBody: moatParams,
		Encode:      base.EncodeForm,
		Headers:     header,
	}

	var apiRespStruct BzrResponse
	apiUrl := fmt.Sprintf("%s?%s", ServiceUri, header["querystring"])
	resp, err := client.HttpPost(ctx, apiUrl, opts)
	zlog.Infof(ctx, "zbcore, arg:%+v,", arg)
	if err != nil {
		zlog.Warnf(ctx, "ral request failed, %s", err)

		// 需要封装一下错误输出
		return nil, apiError(NetworkError, "")
	}

	if err = DecodeResponse(ctx, resp, &apiRespStruct, output); err != nil {
		zlog.Warnf(ctx, "ral decode failed, output:(%T), %s", output, err)
		return nil, err
	}

	return &apiRespStruct, nil
}

func apiError(errNo int, errMsg string) base.Error {
	errStr := _errNoMsgMap[errNo]
	if errMsg != "" {
		errStr = errStr + ": " + errMsg
	}
	return base.Error{
		ErrNo:  errNo,
		ErrMsg: errStr,
	}
}
