package touchmisgo

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/libs/utils"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.TouchMisGo,
	}
	return c
}

const (
	callRecordListAPI = "/touchmisgo/call/record/list"
)

// CallRecordList 获取通话记录列表
func (c *Client) CallRecordList(ctx *gin.Context, params CallRecordListParams) (resp *CallRecordListResp, err error) {
	req := map[string]interface{}{
		"toUid":     params.ToUid,
		"startTime": params.StartTime,
		"endTime":   params.EndTime,
		"pn":        params.Pn,
		"rn":        params.Rn,
	}

	if params.SourceType > 0 {
		req["sourceType"] = params.SourceType
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, callRecordListAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "TouchMisGo CallRecordList request failed, err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp = &CallRecordListResp{}
	if _, err = api.DecodeResponse(ctx, res, resp); err != nil {
		return
	}

	return resp, nil
}
