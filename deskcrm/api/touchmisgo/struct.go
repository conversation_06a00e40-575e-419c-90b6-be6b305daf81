package touchmisgo

// CallRecordListParams 通话记录列表请求参数
type CallRecordListParams struct {
	ToUid      []int64 `json:"toUid"`      // 被叫用户ID列表
	StartTime  int64   `json:"startTime"`  // 开始时间戳
	EndTime    int64   `json:"endTime"`    // 结束时间戳
	Pn         int     `json:"pn"`         // 页码
	Rn         int     `json:"rn"`         // 每页数量
	SourceType int     `json:"sourceType"` // 来源类型（可选）
}

// CallRecordListResp 通话记录列表响应
type CallRecordListResp struct {
	List  []CallRecord `json:"list"`  // 通话记录列表
	Total int          `json:"total"` // 总数
}

// CallRecord 通话记录
type CallRecord struct {
	CallId     string `json:"callId"`     // 通话ID
	CallMode   int    `json:"callMode"`   // 通话模式
	SourceType int    `json:"sourceType"` // 来源类型
	FromPhone  string `json:"fromPhone"`  // 主叫号码
	FromUid    int64  `json:"fromUid"`    // 主叫用户ID
	ToUid      int64  `json:"toUid"`      // 被叫用户ID
	StartTime  int64  `json:"startTime"`  // 开始时间
	Duration   int    `json:"duration"`   // 通话时长（毫秒）
	CallResult int    `json:"callResult"` // 通话结果
	CreateTime int64  `json:"createTime"` // 创建时间
	DeviceUid  int64  `json:"deviceUid"`  // 设备UID
}
