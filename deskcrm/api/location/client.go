package location

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/libs/utils"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.Location,
	}
	return c
}

const (
	getPhoneLocationAPI = "/location/api/phone"
)

// GetPhoneLocation 获取电话号码的城市信息
func (c *Client) GetPhoneLocation(ctx *gin.Context, phone string) (resp *PhoneLocationResp, err error) {
	req := map[string]interface{}{
		"phone": phone,
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getPhoneLocationAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "Location GetPhoneLocation request failed, phone: %s, err:%v", phone, err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp = &PhoneLocationResp{}
	if _, err = api.DecodeResponse(ctx, res, resp); err != nil {
		return
	}

	return resp, nil
}
