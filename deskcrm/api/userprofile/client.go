package userprofile

import (
	"crypto/md5"
	"deskcrm/conf"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"git.zuoyebang.cc/fwyybase/fwyylibs/api/decode"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	json "github.com/json-iterator/go"
)

const (
	appId = "6D6ADB669D1C692E0499"
)

type AuthParams struct {
	AppId string `json:"appId" form:"appId"`
	Ts    int64  `json:"ts" form:"ts"`
}

func (ap *AuthParams) initAuth() {
	ap.AppId = appId
	ap.Ts = time.Now().Unix()
}

const (
	loginCheckApi        = "/userprofile/login/check"
	getUsersSitesApi     = "/userprofile/api/getuserssites"
	pathCheckRoleApi     = "/userprofile/api/checkrole"
	getDeviceInfoListApi = "/userprofile/api/getdeviceinfolist"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.UserProfile,
	}
	return c
}

func (c *Client) LoginCheck(ctx *gin.Context) (userId int, err error) {
	var output struct {
		UserId   int    `json:"userId"`
		UserName string `json:"userName"`
		Uname    string `json:"uname"`
	}
	err = c.ralRequest(ctx, loginCheckApi, map[string]interface{}{}, nil, &output)
	if err != nil {
		return
	}
	return output.UserId, nil
}

func (c *Client) GetUserInfo(ctx *gin.Context, uid int) (userInfo *UserInfo, err error) {
	data := map[string]interface{}{
		"userId":                     uid,
		"needGetBusinessUids":        "1",
		"needGetSelectedBusinessUid": "1",
	}

	headers := map[string]string{
		"Referer": "/assistantdesk/view/first-line-teacher/my-live",
	}
	var output struct {
		Record *UserInfo
	}
	err = c.ralRequest(ctx, getUsersSitesApi, data, headers, &output)
	if err != nil {
		return
	}
	return output.Record, nil
}

// CheckRole 检查用户是否有接口权限
func (c *Client) CheckRole(ctx *gin.Context, userId int) (hasRole bool, err error) {
	uri := ctx.Request.RequestURI
	uriSps := strings.Split(uri, "?")
	if len(uriSps) == 0 {
		return false, nil
	}
	path := uriSps[0]
	zlog.Info(ctx, uri, uriSps, path)

	return c.CheckRouteRole(ctx, userId, path)
}

func (c *Client) CheckRouteRole(ctx *gin.Context, userId int, route string) (hasRole bool, err error) {
	var output struct {
		HasRole bool `json:"hasRole"`
	}
	err = c.ralRequest(ctx, pathCheckRoleApi, map[string]interface{}{
		"userId": userId,
		"route":  route,
	}, nil, &output)
	zlog.Infof(ctx, "CheckRouteRole userId=%v,uri=%v, err=%+v, output=%+v", userId, ctx.Request.RequestURI, err, output)
	if err != nil {
		return false, err
	}
	return output.HasRole, nil
}

// GetDeviceInfoList 获取设备信息列表
func (c *Client) GetDeviceInfoList(ctx *gin.Context, deviceUids []int64) (deviceInfoMap map[int64]*DeviceInfo, err error) {
	if len(deviceUids) == 0 {
		return make(map[int64]*DeviceInfo), nil
	}

	// 转换为字符串数组
	deviceUidStrs := make([]string, len(deviceUids))
	for i, uid := range deviceUids {
		deviceUidStrs[i] = strconv.FormatInt(uid, 10)
	}

	data := map[string]interface{}{
		"deviceUids": strings.Join(deviceUidStrs, ","),
	}

	var output struct {
		List []*DeviceInfo `json:"list"`
	}
	err = c.ralRequest(ctx, getDeviceInfoListApi, data, nil, &output)
	if err != nil {
		return
	}

	// 转换为 map
	deviceInfoMap = make(map[int64]*DeviceInfo)
	for _, info := range output.List {
		if info != nil {
			deviceInfoMap[info.DeviceUid] = info
		}
	}

	return deviceInfoMap, nil
}

func (c *Client) ralRequest(ctx *gin.Context, urlPath string, data map[string]interface{}, headers map[string]string, output interface{}) error {
	err := encrypt(conf.API.Mesh.AppKey, data)
	if err != nil {
		return err
	}

	passport, _ := ctx.Cookie("ZYBUSS")
	ips, _ := ctx.Cookie("ZYBIPSCAS")
	skip, _ := ctx.Cookie("SKIP")
	dockerIps, _ := ctx.Cookie("DOCKERIPS")

	cookies := map[string]string{
		"ZYBIPSCAS": ips,
		"ZYBUSS":    passport,
		"SKIP":      skip,
		"DOCKERIPS": dockerIps,
	}
	opt := base.HttpRequestOptions{
		RequestBody: data,
		Cookies:     cookies,
		Headers:     headers,
	}
	resp, err := c.cli.HttpPost(ctx, urlPath, opt)

	if err != nil {
		zlog.Warnf(ctx, "ral failed, url[%s] Detail[%+v], err:%s", urlPath, data, err)
		return err
	}

	type LowerCaseResponse struct {
		ErrNo  int         `json:"errNo"`
		ErrStr string      `json:"errstr"`
		Data   interface{} `json:"data"`
	}

	err = decode.DecodeResponse(ctx, resp, &decode.LowerCaseResponse{}, output)
	if err != nil {
		zlog.Warnf(ctx, "decode failed, url[%s] Detail[%+v], err:%s, opt:[%+v]", urlPath, data, err, opt)
	}
	return nil
}

func encrypt(appID string, data map[string]interface{}) error {
	timestamp := time.Now().Unix()
	data["ts"] = timestamp

	appKey, err := genAppKey(appID, data)
	if err != nil {
		return err
	}

	data["appKey"] = appKey
	data["appId"] = appID

	return nil
}

func genAppKey(appID string, data map[string]interface{}) (string, error) {
	for k, v := range data {
		switch v.(type) {
		case string:
		case int64:
			data[k] = strconv.Itoa(int(v.(int64)))
		case int:
			data[k] = strconv.Itoa(v.(int))
		default:
			return "", fmt.Errorf("the type of value in param map is invalid. k:%v, v:%v, data:%v", k, v, data)
		}
	}

	bs, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	// 为了和PHP的默认json_encode行为等价, 需要替换一下
	jsonStr := strings.ReplaceAll(string(bs), `/`, `\/`)

	h := md5.New()
	if _, err := io.WriteString(h, appID); err != nil {
		return "", err
	}
	if _, err := io.WriteString(h, jsonStr); err != nil {
		return "", err
	}
	appKey := fmt.Sprintf("%x", h.Sum(nil))

	return appKey, nil
}
