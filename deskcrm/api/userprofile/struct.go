package userprofile

type UserInfo struct {
	Avatar                 string  `json:"avatar"`
	ThumbnailSquareAvator  string  `json:"thumbnailSquareAvator"`
	Mail                   string  `json:"mail"`
	Name                   string  `json:"name"`
	Phone                  string  `json:"phone"`
	PhoneType              int     `json:"phoneType"`
	UserId                 int     `json:"userId"`
	UserName               string  `json:"userName"`
	UserPhone              string  `json:"userPhone"`
	StaffUid               int     `json:"staffUid"`
	PreSelectedBusinessUid int64   `json:"preSelectedBusinessUid"`
	SelectedBusinessUid    int64   `json:"selectedBusinessUid"`
	BusinessUids           []int64 `json:"businessUids"`
	MenuKey                string  `json:"menuKey"` // 菜单权限标识，对应PHP版本中的menuKey字段
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	DeviceUid int64  `json:"deviceUid"` // 设备UID
	Nickname  string `json:"nickname"`  // 昵称
	Name      string `json:"name"`      // 姓名
	Phone     string `json:"phone"`     // 电话
}
