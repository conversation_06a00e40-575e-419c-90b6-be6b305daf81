package coursetransgo

type GetCourseTransListByStaffLeadsCourseParam struct {
	StaffUid        int64   `json:"staffUid"`
	TradeStartTime  int64   `json:"tradeStartTime"`
	TradeEndTime    int64   `json:"tradeEndTime"`
	RefundStartTime int64   `json:"refundStartTime"`
	RefundEndTime   int64   `json:"refundEndTime"`
	Refund          bool    `json:"refund"`
	LeadsIds        []int64 `json:"leadsIds"`
	LeadsCourseIds  []int64 `json:"leadsCourseIds"`
	IsValid         int64   `json:"isValid"`
}

type GetCourseTransListByStaffLeadsCourseInfo struct {
	LeadsId   int64 `json:"leadsId"`
	IsValid   int64 `json:"isValid"`
	SubjectId int64 `json:"subjectId"`
}

type GetCourseTransListByStaffLeadsCourseResp struct {
	List  []GetCourseTransListByStaffLeadsCourseInfo `mapstructure:"list"`
	Total int                                        `mapstructure:"total"`
}

type GetRelationTransByStaffReq struct {
	StaffUid  int64   `json:"staffUid" form:"staffUid"`
	DeviceUid int64   `json:"deviceUid" form:"deviceUid"`
	LeadsId   []int64 `json:"leadsId" form:"leadsId"`
	IsValid   int64   `json:"isValid" form:"isValid"`
	BeginTime int64   `json:"beginTime" form:"beginTime"`
	EndTime   int64   `json:"endTime" form:"endTime"`
	TransType int64   `json:"transType" form:"transType"`
}
type GetRelationTransByStaffResp struct {
	List  []GetRelationTransByStaffItem `json:"list"`
	Total int64                         `json:"total"`
}

type GetRelationTransByStaffItem struct {
	LeadsId   int64 `json:"leadsId"`
	IsValid   int64 `json:"isValid"`
	SubjectId int64 `json:"subjectId"`
	Refund    int64 `json:"refund"`
	TradeFee  int64 `json:"tradeFee"`
	PvCount   int64 `json:"pvCount"`
	CustomUid int64 `json:"customUid"`
}

type BatchGetLastFromDetailParams struct {
	LastFromList []string `json:"lastFromList" form:"lastFromList"`
}

type BatchGetLastFromDetailResp struct {
	List  []*BatchGetLastFromDetail `json:"list"`
	Total int64                     `json:"total"`
}

type BatchGetLastFromDetail struct {
	ID       int64  `json:"id"`
	LastFrom string `json:"lastFrom"`
	Name     string `json:"name"`
}

// GetCourseTransListByStaffStudentResp 根据员工和学生获取转课数据响应
type GetCourseTransListByStaffStudentResp struct {
	List  []ScTransData `json:"list"`
	Total int64         `json:"total"`
}

// ScTransData 转课数据项
type ScTransData struct {
	ID              int64  `json:"id"`
	LeadsId         int64  `json:"leadsId"`
	ScUid           int64  `json:"scUid"`
	IsValid         int    `json:"isValid"`
	LeadsCourseId   int64  `json:"leadsCourseId"`
	CourseId        int64  `json:"courseId"`
	CustomUid       int64  `json:"customUid"`
	SubTradeId      string `json:"subTradeId"`
	TradeId         string `json:"tradeId"`
	RefundTime      int64  `json:"refundTime"`
	TradeTime       int64  `json:"tradeTime"`
	Refund          int64  `json:"refund"`
	ChangeType      int    `json:"changeType"`
	TransTime       int64  `json:"transTime"`
	Season          int    `json:"season"`
	TradeFee        int64  `json:"tradeFee"`
	IsBoundDiscount int    `json:"isBoundDiscount"`
	OriginalTradeId string `json:"originalTradeId"`
	BuyType         int    `json:"buyType"`
}
