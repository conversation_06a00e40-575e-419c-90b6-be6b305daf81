package coursetransgo

import (
	"deskcrm/api"
	"deskcrm/components"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"encoding/json"

	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.CourseTransGo,
	}
	return c
}

const (
	getCourseTransListByStaffLeadsCourseAPI = "/coursetransgo/api/trans/get-course-trans-list-by-staff-leads-course"
	getCourseTransListByStaffStudentAPI     = "/coursetransgo/api/trans/get-course-trans-list-by-staff-student"
	apiGetRelationTransByStaff              = "/coursetransgo/api/trans/get-relation-trans-by-staff"
	apiBatchGetLastFromDetail               = "/coursetransgo/api/lastfrom/getlastfromlist" //批量获取lastfrom信息
	apiGetRelationTransByStaffMax           = 300
)

func (c *Client) GetCourseTransListByStaffLeadsCourse(ctx *gin.Context, lpcUid int64, courseId []int64, leadsId []int64) (response map[int64][]*GetCourseTransListByStaffLeadsCourseInfo, err error) {
	req := map[string]interface{}{
		"staffUid":        lpcUid,
		"tradeStartTime":  0,
		"tradeEndTime":    0,
		"refundStartTime": 0,
		"refundEndTime":   0,
		"refund":          0,
		"leadsIds":        components.Array.JoinArrayInt64ToString(components.Array.UniqueInt64(leadsId), ","),
		"leadsCourseIds":  components.Array.JoinArrayInt64ToString(courseId, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getCourseTransListByStaffLeadsCourseAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}
	resp := GetCourseTransListByStaffLeadsCourseResp{}
	response = make(map[int64][]*GetCourseTransListByStaffLeadsCourseInfo)
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, item := range resp.List {
		if _, exists := response[item.LeadsId]; !exists {
			response[item.LeadsId] = []*GetCourseTransListByStaffLeadsCourseInfo{}
		}
		response[item.LeadsId] = append(response[item.LeadsId], &GetCourseTransListByStaffLeadsCourseInfo{
			IsValid:   item.IsValid,
			LeadsId:   item.LeadsId,
			SubjectId: item.SubjectId,
		})
	}

	return response, nil
}

// GetCourseTransListByStaffStudent 根据员工和学生获取转课数据
func (c *Client) GetCourseTransListByStaffStudent(ctx *gin.Context, scUid int64, customUid int64, courseIds []int64) ([]ScTransData, error) {
	req := map[string]interface{}{
		"staffUid":       scUid,
		"customUid":      customUid,
		"leadsCourseIds": components.Array.JoinArrayInt64ToString(courseIds, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getCourseTransListByStaffStudentAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetCourseTransListByStaffStudent request err: %v", err)
		return nil, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	var resp GetCourseTransListByStaffStudentResp
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	return resp.List, nil
}

func (c *Client) GetRelationTransByStaff(ctx *gin.Context, req GetRelationTransByStaffReq) (resp GetRelationTransByStaffResp, err error) {
	chunkLeadsId := fwyyutils.ChunkArrayInt64(req.LeadsId, apiGetRelationTransByStaffMax)
	var temp GetRelationTransByStaffResp
	for _, chunked := range chunkLeadsId {
		params := map[string]interface{}{
			"staffUid": req.StaffUid,
			"leadsId":  chunked,
			"isValid":  req.IsValid,
		}

		opts := base.HttpRequestOptions{
			RequestBody: params,
			Encode:      base.EncodeJson,
		}
		utils.DecorateHttpOptions(ctx, &opts)

		res, _err := c.cli.HttpPost(ctx, apiGetRelationTransByStaff, opts)
		if _err != nil {
			err = _err
			zlog.Warnf(ctx, "request remote api err:%v", err)
			return
		}

		if err = api.ApiHttpCode(ctx, res); err != nil {
			return
		}
		if _, err = api.DecodeResponse(ctx, res, &temp); err != nil {
			return
		}

		if resp.List == nil {
			resp.List = make([]GetRelationTransByStaffItem, 0)
		}
		if temp.List != nil {
			resp.List = append(resp.List, temp.List...)
		}
		resp.Total += temp.Total
	}

	return
}

func (c *Client) BatchGetLastFromDetail(ctx *gin.Context, params BatchGetLastFromDetailParams) (lastFromList []*BatchGetLastFromDetail, err error) {
	resp := BatchGetLastFromDetailResp{}
	req := map[string]interface{}{}
	temp, _ := json.Marshal(params)
	if err = json.Unmarshal(temp, &req); err != nil {
		zlog.Warnf(ctx, "jsonUnMarshalErr:%v", err)
		return
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, apiBatchGetLastFromDetail, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		zlog.Warnf(ctx, "GetWxStudentListData DecodeResponse err:%v,data:%v", err, string(res.Response))
		return
	}

	return resp.List, nil

}
