package allocate

type NormalLeadsByCourseAssistantResp struct {
	List []*NormalLeadsByCourseAssistant `json:"list"`
}

type NormalLeadsByCourseAssistant struct {
	StudentUid int64  `json:"studentUid"`
	LeadsId    int64  `json:"leadsId"`
	CourseId   int64  `json:"CourseID"`
	AllocTime  int64  `json:"allocTime"`
	ExtData    string `json:"extData"`
}

type NormalLeadsByCourseAssistantExtData struct {
	ScRemark     string
	ScRemarkTime int
	StudentUid   int64 `json:"studentUid"`
	LeadsId      int64 `json:"leadsId"`
	AllocTime    int64 `json:"allocTime"`
}

const (
	LeadsInfoStatusInvalid = 3
	LeadsretrieveType      = 100007
)

type LeadsInfo struct {
	StudentUid      int64  `json:"studentUid"`      // 学生UID
	CourseId        int64  `json:"courseId"`        // 课程ID
	LeadsId         int64  `json:"leadsId"`         // 线索ID
	PersonUid       int64  `json:"personUid"`       // 人员UID
	ClassId         int64  `json:"classId"`         // 班级ID
	StudentType     int64  `json:"studentType"`     // 学生类型
	TransferType    int64  `json:"transferType"`    // 转移类型
	Grade           int64  `json:"grade"`           // 年级
	Subject         int64  `json:"subject"`         // 学科
	IsOriginal      int64  `json:"isOriginal"`      // 是否原始
	Status          int64  `json:"status"`          // 状态
	TradeTime       int64  `json:"tradeTime"`       // 交易时间
	RefundTime      int64  `json:"refundTime"`      // 退款时间
	StudentArea     int64  `json:"studentArea"`     // 学生区域
	AssistantArea   int64  `json:"assistantArea"`   // 助教区域
	InviterUid      int64  `json:"inviterUid"`      // 邀请人UID
	ExtData         string `json:"extData"`         // 扩展数据
	UserId          int64  `json:"userId"`          // 用户ID
	WxMapId         int64  `json:"wxMapId"`         // 微信映射ID
	SellChannelId   int64  `json:"sellChannelId"`   // 销售渠道ID
	BuyType         int64  `json:"buyType"`         // 购买类型
	ChannelId       int64  `json:"channelId"`       // 渠道ID
	SaleMode        int64  `json:"saleMode"`        // 销售模式
	AllocTime       int64  `json:"allocTime"`       // 分配时间
	ExpireTimeStart int64  `json:"expireTimeStart"` // 过期开始时间
	ExpireTime      int64  `json:"expireTime"`      // 过期时间
	InvalidReason   string `json:"invalidReason"`   // 无效原因
	IsRealAlloc     int64  `json:"isRealAlloc"`     // 是否真实分配
	IsDeleted       int64  `json:"isDeleted"`       // 是否删除
	Phone           int64  `json:"phone"`           // 电话
	PhoneStr        string `json:"phoneStr"`        // 电话字符串
	ActivityId      int64  `json:"activityId"`      // 活动ID
	Source          int64  `json:"source"`          // 来源
	Level           int64  `json:"level"`           // 等级
	Score           int64  `json:"score"`           // 分数
	LastFrom        string `json:"lastFrom"`        // 最后来源
	Stage           int64  `json:"stage"`           // 阶段
	TransTimeStart  int64  `json:"transTimeStart"`  // 转换开始时间
	SubTradeId      int64  `json:"subTradeId"`      // 子交易ID
	LeadsOrigin     int64  `json:"leadsOrigin"`     // 线索来源
	FromPersonUid   int64  `json:"fromPersonUid"`   // 来源人员UID
	FromUserId      int64  `json:"fromUserId"`      // 来源用户ID
	FromWxMapId     int64  `json:"fromWxMapId"`     // 来源微信映射ID
	ActiveStatus    int64  `json:"activeStatus"`    // 活跃状态
	ActiveTime      int64  `json:"activeTime"`      // 活跃时间
	TransferTime    int64  `json:"transferTime"`    // 转移时间
	CreateTime      int64  `json:"createTime"`      // 创建时间
	UpdateTime      int64  `json:"updateTime"`      // 更新时间
	IsGrey          bool   `json:"isGrey"`          // 是否灰度
	IsValid         bool   `json:"isValid"`         // 是否有效
	IsExcluded      int64  `json:"isExcluded"`      // 是否排除
	ServiceType     int64  `json:"serviceType"`     // 服务类型
	IsInherit       int64  `json:"isInherit"`       // 是否继承
	DetailId        int64  `json:"detailId"`        // 详情ID
	AllocData       string `json:"allocData"`       // 分配数据
}

type NoCourseLeadsInfo struct {
	StudentUid int64 `json:"studentUid"`
	CourseId   int64 `json:"courseId"`
	LeadsId    int64 `json:"leadsId"`
	AllocTime  int64 `json:"allocTime"`
	ExpireTime int64 `json:"expireTime"`
	ActivityId int64 `json:"activityId"`
}
type NoCourseLeadsResp struct {
	List []NoCourseLeadsInfo `json:"list"`
}

type NoClassLeadsResp struct {
	List []*NoClassLeads `json:"list"`
}

type NoClassLeads struct {
	StudentUid int64  `json:"studentUid"`
	LeadsId    int64  `json:"leadsId"`
	CourseId   int64  `json:"CourseID"`
	AllocTime  int64  `json:"allocTime"`
	ExtData    string `json:"extData"`
}

// GetLeadsByBatchCourseIdUid 相关结构体
type BatchCourseIdUidParam struct {
	StuUid   int64 `json:"stuUid"`
	CourseId int64 `json:"courseId"`
}

type BatchLeadsInfo struct {
	LeadsId       int64  `json:"leadsId"`
	StudentUid    int64  `json:"studentUid"`
	CourseId      int64  `json:"courseId"`
	AllocTime     int64  `json:"allocTime"`
	Grade         int64  `json:"grade"`
	Subject       int64  `json:"subject"`
	PersonUid     int64  `json:"personUid"`
	Status        int64  `json:"status"`
	ClassId       int64  `json:"classId"`
	StudentType   int64  `json:"studentType"`
	TransferType  int64  `json:"transferType"`
	IsOriginal    int64  `json:"isOriginal"`
	TradeTime     int64  `json:"tradeTime"`
	RefundTime    int64  `json:"refundTime"`
	StudentArea   int64  `json:"studentArea"`
	AssistantArea int64  `json:"assistantArea"`
	InviterUid    int64  `json:"inviterUid"`
	ExtData       string `json:"extData"`
	UserId        int64  `json:"userId"`
	WxMapId       int64  `json:"wxMapId"`
	SellChannelId int64  `json:"sellChannelId"`
	BuyType       int64  `json:"buyType"`
	ChannelId     int64  `json:"channelId"`
	SaleMode      int64  `json:"saleMode"`
	ExpireTime    int64  `json:"expireTime"`
	InvalidReason string `json:"invalidReason"`
	IsRealAlloc   int64  `json:"isRealAlloc"`
	IsDeleted     int64  `json:"isDeleted"`
	Phone         int64  `json:"phone"`
	PhoneStr      string `json:"phoneStr"`
	ActivityId    int64  `json:"activityId"`
	Source        int64  `json:"source"`
	Level         int64  `json:"level"`
	Score         int64  `json:"score"`
	LastFrom      string `json:"lastFrom"`
	Stage         int64  `json:"stage"`
	SubTradeId    int64  `json:"subTradeId"`
	LeadsOrigin   int64  `json:"leadsOrigin"`
	FromPersonUid int64  `json:"fromPersonUid"`
	FromUserId    int64  `json:"fromUserId"`
	FromWxMapId   int64  `json:"fromWxMapId"`
	ActiveStatus  int64  `json:"activeStatus"`
	ActiveTime    int64  `json:"activeTime"`
	TransferTime  int64  `json:"transferTime"`
	CreateTime    int64  `json:"createTime"`
	UpdateTime    int64  `json:"updateTime"`
	IsGrey        bool   `json:"isGrey"`
	IsValid       bool   `json:"isValid"`
	IsExcluded    int64  `json:"isExcluded"`
	ServiceType   int64  `json:"serviceType"`
	IsInherit     int64  `json:"isInherit"`
	DetailId      int64  `json:"detailId"`
	AllocData     string `json:"allocData"`
}

type BatchLeadsData struct {
	LeadsList []BatchLeadsInfo `json:"leadsList"`
}
