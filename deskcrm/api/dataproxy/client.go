package dataproxy

import (
	"deskcrm/api"
	"deskcrm/components"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.DataProxy,
	}
	return c
}

const (
	getDimStudentListAPI                          = "/dataproxy/student/get-student-list"
	getStudentInfoOnlineAPI                       = "/dataproxy/student/get-student-info-online"
	getLiveEsInteractQuestion                     = "/dataproxy/livestreamInteractQuestion/getInteractQuestionCntList"
	getEsCuData                                   = "/dataproxy/cu/getListByCourseIdsStudentUidsAssistantUids"
	getCommonLuData                               = "/dataproxy/lu/get-common-list-by-lesson-students"
	getCommonCuData                               = "/dataproxy/cu/get-common-list-by-course-students"
	getAssistantCourseData                        = "/dataproxy/qiwei-course/get-assistant-lesson-student-list"
	getExternalIdlLpcLeadsL2New                   = "/dataproxy/leads/get-external-idl-lpc-leads-data-by-leadsid-new"
	getListByCourseIdLessonIdsAssistantUid        = "/dataproxy/lu/getListByCourseIdLessonIdsAssistantUid"
	getListByBindIdsBindTypeRelationTypesExamTags = "/dataproxy/examRelation/getListByBindIdsBindTypeRelationTypesExamTags"
	getLpcListByLessonLeads                       = "/dataproxy/lu/get-lpc-list-by-lesson-leads"
	getListByBindIdsBindTypeRelationTypes         = "/dataproxy/examRelation/getListByBindIdsBindTypeRelationTypes"
	getIdlLpcLeadsDataByLeadsIds                  = "/dataproxy/leads/get-idl-lpc-leads-data-by-leads"
	getIdlLpcLeadsDataByStudentUids               = "/dataproxy/leads/get-idl-lpc-leads-data-by-students"
	getExternalIdlLpcLeadsDataByLeadsid           = "/dataproxy/leads/get-external-idl-lpc-leads-data-by-leadsid"
	getLpcListByCourseLeads                       = "/dataproxy/lu/get-lpc-list-by-course-leads"
	getLpcListByCourseStudent                     = "/dataproxy/lu/get-lpc-list-by-course-student"

	getEsCuOrderByCA = "/dataproxy/cuOrder/getListByCourseIdAssistantUid"
	getEsCuOrderByCU = "/dataproxy/cuOrder/getListByStudentUids"

	getEsOrderByCU = "/dataproxy/order/get-order-assistant-list-by-cu"

	getCourseLessonCommonDataNew          = "/dataproxy/sopclu/get-course-lesson-common-data-new"
	getCourseStudentAggr                  = "/dataproxy/sopcu/get-course-student-aggr"
	getEsStudentAppDataAPI                = "/dataproxy/student/get-student-app-info"
	getEsIdlUData                         = "/dataproxy/student/get-u-data"
	getEsStudentOrderDetail               = "/dataproxy/cuOrder/getStudentTradeOrderInfo"
	getEsPublicSeaClueInfo                = "/dataproxy/relation/publicsea/cluelist"
	getListByUnitIdStudentUids            = "/dataproxy/unitStudent/getListByUnitIdStudentUids"
	getEsStudentExprCntDataAPI            = "/dataproxy/student/get-student-expr-cnt"
	getEsStudentLatestDelamination        = "/dataproxy/cuSaveTime/getListByCourseIdAssistantUidStudentUid"
	getListByCourseIdAssistantUidSaveTime = "/dataproxy/cuSaveTime/getListByCourseIdAssistantUidSaveTime"
	getEsCityDataAPI                      = "/dataproxy/area/get_city_info"
)

func (c *Client) GetCourseLessonCommonDataNew(ctx *gin.Context, params GetCourseLessonCommonDataNewParams) (studentList []*GetCourseLessonCommonDataNewResp, err error) {
	req := map[string]interface{}{
		"courseId":    params.CourseId,
		"lessonId":    params.LessonId,
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getCourseLessonCommonDataNew, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetCourseLessonCommonDataNewResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetCourseStudentAggr(ctx *gin.Context, params GetCourseStudentAggrParams) (studentList []*GetCourseStudentAggrResp, err error) {
	req := map[string]interface{}{
		"courseId":    params.CourseId,
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getCourseStudentAggr, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetCourseStudentAggrResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetDimStudentList(ctx *gin.Context, params GetDimStudentListParams) (studentList []*GetDimStudentListResp, err error) {
	req := map[string]interface{}{
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getDimStudentListAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetDimStudentListResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetStudentInfoOnline(ctx *gin.Context, params GetEsUONParams) (studentList []*GetEsUONResp, err error) {
	req := map[string]interface{}{
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getStudentInfoOnlineAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	zlog.Infof(ctx, "GetStudentInfoOnline res %v", string(res.Response))
	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetEsUONResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetLiveEsInteractQuestion(ctx *gin.Context, params GetLiveEsInteractQuestionParam) (studentList []*GetLiveEsInteractQuestionResp, err error) {
	req := map[string]interface{}{
		"courseId":    params.CourseId,
		"lessonId":    params.LessonId,
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getLiveEsInteractQuestion, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	for _, detail := range resp.List {
		t := &GetLiveEsInteractQuestionResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetEsCuData(ctx *gin.Context, params GetEsCuDataParam) (studentList []*GetEsCuDataResp, err error) {
	req := map[string]interface{}{
		"courseIds":     components.Array.JoinArrayInt64ToString(params.CourseIds, ","),
		"assistantUids": components.Array.JoinArrayInt64ToString(params.AssistantUids, ","),
		"studentUids":   components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":        strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getEsCuData, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetEsCuDataResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetCommonLuData(ctx *gin.Context, params GetCommonLuParam) (studentList []*GetCommonLuResp, err error) {
	req := map[string]interface{}{
		"lessonId":    params.LessonId,
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getCommonLuData, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetCommonLuResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetCommonCuData(ctx *gin.Context, params GetCommonCuParam) (studentList []*GetCommonCuResp, err error) {
	req := map[string]interface{}{
		"courseId":    params.CourseId,
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getCommonCuData, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetCommonCuResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetAssistantCourseLuData(ctx *gin.Context, params GetAssistantCourseLuParam) (respData []GetAssistantCourseLuResp, err error) {
	req := map[string]interface{}{
		"lessonId":    params.LessonId,
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getAssistantCourseData, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	for _, detail := range resp.List {
		var t GetAssistantCourseLuResp
		if err := api.DecodeInterface(ctx, detail, &t); err != nil {
			continue
		}
		respData = append(respData, t)
	}

	return
}

func (c *Client) GetExternalIdlLpcLeadsL2New(ctx *gin.Context, params GetExternalIdlLpcLeadsL2NewParam) (studentList []*GetExternalIdlLpcLeadsL2NewResp, err error) {
	req := map[string]interface{}{
		"leads_ids": components.Array.JoinArrayInt64ToString(params.LeadsIds, ","),
		"fields":    strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getExternalIdlLpcLeadsL2New, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetExternalIdlLpcLeadsL2NewResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetListByCourseIdLessonIdsAssistantUid(ctx *gin.Context, params GetListByCourseIdLessonIdsAssistantUidParam) (studentList []*GetStudentLessonDataCommonResp, err error) {
	req := map[string]interface{}{
		"courseId":     params.CourseId,
		"lessonIds":    components.Array.JoinArrayInt64ToString(params.LessonIds, ","),
		"assistantUid": params.AssistantUid,
		"fields":       strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getListByCourseIdLessonIdsAssistantUid, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetStudentLessonDataCommonResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetListByBindIdsBindTypeRelationTypesExamTags(ctx *gin.Context, params GetListByBindIdsBindTypeRelationTypesCommonParam) (studentList []*GetListByBindIdsBindTypeRelationTypesCommonResp, err error) {
	req := map[string]interface{}{
		"bindType":      params.BindType,
		"bindIds":       params.BindIds,
		"relationTypes": params.RelationTypes,
		"examTags":      params.ExamTags,
		"fields":        params.Fields,
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getListByBindIdsBindTypeRelationTypesExamTags, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetListByBindIdsBindTypeRelationTypesCommonResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetListByBindIdsBindTypeRelationTypes(ctx *gin.Context, params GetListByBindIdsBindTypeRelationTypesCommonParam) (studentList []*GetListByBindIdsBindTypeRelationTypesCommonResp, err error) {
	req := map[string]interface{}{
		"bindType":      params.BindType,
		"bindIds":       params.BindIds,
		"relationTypes": params.RelationTypes,
		"fields":        params.Fields,
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getListByBindIdsBindTypeRelationTypes, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetListByBindIdsBindTypeRelationTypesCommonResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetLpcListByLessonLeads(ctx *gin.Context, params GetLpcListByLessonLeadsParam) (studentList []*GetLpcListByLessonLeadsResp, err error) {
	req := map[string]interface{}{
		"courseId": params.CourseId,
		"lessonId": params.LessonId,
		"leadsIds": components.Array.JoinArrayInt64ToString(params.LeadsIds, ","),
		"fields":   strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getLpcListByLessonLeads, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetLpcListByLessonLeadsResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetIdlLpcLeadsDataByLeadsIds(ctx *gin.Context, leadsIds []int64, fields []string) (studentList []*GetIdlLpcLeadsDataByLeadsIdsResp, err error) {
	req := map[string]interface{}{
		"leadsIds": components.Array.JoinArrayInt64ToString(leadsIds, ","),
		"fields":   strings.Join(fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getIdlLpcLeadsDataByLeadsIds, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetIdlLpcLeadsDataByLeadsIdsResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetExternalIdlLpcLeadsDataByLeadsid(ctx *gin.Context, leadsIds []int64, fields []string) (studentList []*GetExternalIdlLpcLeadsDataByLeadsidResp, err error) {
	req := map[string]interface{}{
		"leads_ids": components.Array.JoinArrayInt64ToString(leadsIds, ","),
		"fields":    strings.Join(fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getExternalIdlLpcLeadsDataByLeadsid, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetExternalIdlLpcLeadsDataByLeadsidResp{}
		if err = api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetLpcListByCourseLeads(ctx *gin.Context, courseId, isPlayback int64, leadsIds []int64, fields []string) (studentList []*GetLpcListByCourseLeadsResp, err error) {
	req := map[string]interface{}{
		"leadsIds":   components.Array.JoinArrayInt64ToString(leadsIds, ","),
		"fields":     strings.Join(fields, ","),
		"courseId":   courseId,
		"isPlayback": isPlayback,
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getLpcListByCourseLeads, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetLpcListByCourseLeadsResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetEsCuOrderByCA(ctx *gin.Context, params GetEsCuOrderByCAParam) (studentList []*GetEsCuOrderByCAResp, err error) {
	req := map[string]interface{}{
		"courseId":     params.CourseId,
		"assistantUid": params.AssistantUid,
		"fields":       strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getEsCuOrderByCA, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetEsCuOrderByCAResp{}
		if err = api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return
}

func (c *Client) GetEsCuOrderByCU(ctx *gin.Context, params GetEsCuOrderByCUParam) (studentList []*GetEsCuOrderByCUResp, err error) {
	req := map[string]interface{}{
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
		"startTime":   time.Now().AddDate(-3, 0, 0).Unix(),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getEsCuOrderByCU, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetEsCuOrderByCUResp{}
		if err = api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return
}

func (c *Client) GetEsStudentAppData(ctx *gin.Context, params GetEsStudentAppDataParam) (studentList []*GetEsStudentAppDataResp, err error) {
	req := map[string]interface{}{
		"appType":     params.AppType,
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getEsStudentAppDataAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetEsStudentAppDataResp{}
		if err = api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return
}

func (c *Client) GetEsIdlUData(ctx *gin.Context, params GetEsIdlUDataParam) (studentList []*GetEsIdlUDataResp, err error) {
	req := map[string]interface{}{
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getEsIdlUData, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetEsIdlUDataResp{}
		if err = api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return
}

func (c *Client) GetStudentOrderDetailList(ctx *gin.Context, params GetStudentOrderDetailReq) (studentList []*GetStudentOrderDetailResp, err error) {
	req := map[string]interface{}{
		"studentUidList": params.StudentUidList,
		"fields":         params.Fields,
		"shopId":         params.ShopId,
		"skuId":          params.SkuId,
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getEsStudentOrderDetail, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}
	if err = api.ApiHttpCode(ctx, res); err != nil {
		zlog.Warnf(ctx, "GetStudentOrderDetailList ApiHttpCode req=%v,err=%v", req, err)
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetStudentOrderDetailResp{}
		if err = api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return
}

func (c *Client) GeDataPublicSeaClueInfo(ctx *gin.Context, params GetEsPublicSeaClueInfoReq) (clueInfoList []*GetEsPublicSeaClueInfoResp, err error) {
	req := map[string]interface{}{
		"orClueIds": params.ClueIds,
		"fields":    params.Fields,
		"rn":        1000,
		"pn":        1,
		"filter":    map[string]interface{}{},
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getEsPublicSeaClueInfo, opts)
	if err != nil {
		zlog.Warnf(ctx, "request remote api err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetEsPublicSeaClueInfoResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		clueInfoList = append(clueInfoList, t)
	}
	return clueInfoList, nil
}

func (c *Client) GetStudentExprInfoData(ctx *gin.Context, params GetStudentExprInfoDataReq) (studentList []*GetStudentExprInfoDataResp, err error) {
	req := map[string]interface{}{
		"courseId":    params.CourseId,
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getEsStudentExprCntDataAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetStudentExprInfoDataResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetStudentOrderDataByCU(ctx *gin.Context, params GetStudentOrderDataByCUReq) (studentList []*GetStudentOrderDataByCUResp, err error) {
	req := map[string]interface{}{
		"courseId":    params.CourseId,
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
		"orderTime":   time.Now().AddDate(-1, 0, 0).Unix(),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getEsOrderByCU, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetStudentOrderDataByCUResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetListByUnitIdStudentUids(ctx *gin.Context, params GetListByUnitIdStudentUidsReq) (studentList []*GetListByUnitIdStudentUidsResp, err error) {
	req := map[string]interface{}{
		"unitId":      params.UnitID,
		"studentUids": components.Array.JoinArrayInt64ToString(params.StudentUids, ","),
		"fields":      strings.Join(params.Fields, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getListByUnitIdStudentUids, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetListByUnitIdStudentUids:%v", err)
		return
	}
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetListByUnitIdStudentUidsResp{}
		if err = api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return
}

func (c *Client) GetStudentLatestDelaminationDataByCA(ctx *gin.Context, params GetStudentLatestDelaminationDataByCAReq) (studentList []*GetStudentLatestDelaminationDataByCAResp, err error) {
	req := map[string]interface{}{
		"courseId":     params.CourseId,
		"studentUid":   params.StudentUid,
		"assistantUid": params.AssistantUid,
		"fields":       strings.Join(params.Fields, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getEsStudentLatestDelamination, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetStudentLatestDelaminationDataByCAResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

func (c *Client) GetListByCourseIdAssistantUidSaveTime(ctx *gin.Context, params GetListByCourseIdAssistantUidSaveTimeReq) (studentList map[int64]*GetStudentLatestDelaminationDataByCAResp, err error) {
	if params.SaveTime == "" {
		params.SaveTime = time.Now().Format("20060102")
	}
	req := map[string]interface{}{
		"courseId":     params.CourseId,
		"studentUid":   params.StudentUid,
		"assistantUid": params.AssistantUid,
		"saveTime":     params.SaveTime,
		"fields":       strings.Join(params.Fields, ","),
	}

	studentList = make(map[int64]*GetStudentLatestDelaminationDataByCAResp)

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getListByCourseIdAssistantUidSaveTime, opts)
	if err != nil {
		zlog.Warnf(ctx, "requestMkDocErr:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetStudentLatestDelaminationDataByCAResp{}
		if err := api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList[t.StudentUID] = t
	}
	return studentList, nil
}

func (c *Client) GetLpcListByCourseStudent(ctx *gin.Context, params GetLpcListByCourseStudentParam) (studentList []*GetLpcListByCourseStudentResp, err error) {
	req := map[string]interface{}{
		"courseId":   params.CourseId,
		"studentUid": params.StudentUid,
		"fields":     strings.Join(params.Fields, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getLpcListByCourseStudent, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetLpcListByCourseStudent:%v", err)
		return
	}
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &GetLpcListByCourseStudentResp{}
		if err = api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return
}

// GetIdlLpcLeadsDataByStudentUids 根据学生UID获取LPC leads数据
func (c *Client) GetIdlLpcLeadsDataByStudentUids(ctx *gin.Context, studentUids []int64, fields []string) ([]*IdlLpcLeadsData, error) {
	if len(studentUids) == 0 {
		return []*IdlLpcLeadsData{}, nil
	}

	req := map[string]interface{}{
		"studentUids": components.Array.JoinArrayInt64ToString(studentUids, ","),
		"fields":      strings.Join(fields, ","),
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, getIdlLpcLeadsDataByStudentUids, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetIdlLpcLeadsDataByStudentUids request err: %v", err)
		return nil, err
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	var resp GetIdlLpcLeadsDataResp
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	var studentList []*IdlLpcLeadsData
	for _, detail := range resp.List {
		t := &IdlLpcLeadsData{}
		if err = api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		studentList = append(studentList, t)
	}
	return studentList, nil
}

// GetEsCityData 获取城市信息数据
func (c *Client) GetEsCityData(ctx *gin.Context, cityNames []string) (cityList []*CityDataResp, err error) {
	if len(cityNames) == 0 {
		return cityList, nil
	}

	req := map[string]interface{}{
		"cityNames": strings.Join(cityNames, ","),
		"fields":    "city_level_name,city_name,city_level",
	}

	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpGet(ctx, getEsCityDataAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "DataProxy GetEsCityData request failed, err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := singleRet{}
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}

	for _, detail := range resp.List {
		t := &CityDataResp{}
		if err = api.DecodeInterface(ctx, detail, t); err != nil {
			continue
		}
		cityList = append(cityList, t)
	}
	return cityList, nil
}
