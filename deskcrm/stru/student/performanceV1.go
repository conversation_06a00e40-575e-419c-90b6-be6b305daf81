package student

// BasicData 基础数据结构
type BasicData struct {
	CourseLessonInfos map[int64]interface{} // 课程章节信息
	LessonList        []interface{}         // 章节列表
	LessonIds         []int64               // 章节ID列表
	EndLessonIds      []int64               // 已结束章节ID列表
	ShowLessonIds     []int64               // 显示章节ID列表
	IsLpcCourse       bool                  // 是否LPC课程
	GradeId           int64                 // 年级ID
	ExamTotalNum      map[string]int        // 试卷总数
}

// ProcessedData 处理后的数据结构
type ProcessedData struct {
	CommonLuData              map[string]interface{} // 通用LU数据
	DasStudentLessonInfos     map[string]interface{} // DAS学生章节信息
	LpcLUData                 map[string]interface{} // LPC LU数据
	LpcLessonStrengthPractice map[string]interface{} // LPC巩固练习
	LessonTeacherMap          map[string]interface{} // 教师映射
	LessonReport              map[string]interface{} // 课堂报告
	DeerData                  map[string]interface{} // 小鹿数据
	ScoreInfos                map[string]interface{} // 学分信息
	LearningPlans             map[string]interface{} // 学习计划
	HwBindExams               map[string]interface{} // 作业绑定
	LessonData                map[string]interface{} // 章节数据
}
