package ui

import (
	"deskcrm/api/mesh"
	"deskcrm/api/userprofile"
	"deskcrm/components"
	"deskcrm/controllers/http/ui/output/outputUser"
	"deskcrm/middleware"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// 登录类型常量
const (
	LoginTypePassport = 1 // Passport 登录
	LoginTypeIPS      = 2 // IPS 登录

	WxAppIdGeWechat = 1 // 个微
	WxAppIdQiWechat = 2 // 企微
)

type userService struct{}

var UserService = userService{}

// GetUserInfo 获取用户信息
func (s userService) GetUserInfo(ctx *gin.Context) (*outputUser.UserInfoResp, error) {
	// 1. 获取登录用户信息
	loginUserInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil || loginUserInfo == nil {
		return nil, fmt.Errorf("用户未登录")
	}

	personUid := loginUserInfo.UserId
	assistantUid := loginUserInfo.SelectedBusinessUid

	zlog.Infof(ctx, "GetUserInfo personUid=%d, assistantUid=%d", personUid, assistantUid)

	// 2. 获取助教电话信息
	assistantPhone := ""
	if assistantUid > 0 {
		assistantInfo, err := mesh.NewClient().GetUserInfoByDeviceUid(ctx, assistantUid)
		if err != nil {
			zlog.Warnf(ctx, "GetUserInfoByDeviceUid failed: %v", err)
		} else {
			assistantPhone = assistantInfo.Record.Phone
		}
	}

	// 3. 获取微信AppId
	appId := s.getWxAppId(ctx, assistantUid)

	// 4. 隐藏敏感信息（根据用户确认，灰度已全量，直接隐藏）
	hiddenAssistantPhone := components.Util.HiddenPhone(assistantPhone)
	hiddenPhone := components.Util.HiddenPhone(loginUserInfo.Phone)
	hiddenPersonPhone := components.Util.HiddenPhone(loginUserInfo.UserPhone)

	// 5. 生成水印数据
	watermark := s.generateWatermark(loginUserInfo)

	// 6. 获取会话信息
	loginType, logoutUrl := s.getSessionInfo(ctx)

	// 7. 组装返回结构
	result := &outputUser.UserInfoResp{
		AssistantUid:   assistantUid,
		AssistantPhone: hiddenAssistantPhone,
		Phone:          hiddenPhone,
		GradeGroup:     0,
		PersonUid:      int64(personUid),
		PersonPhone:    hiddenPersonPhone,
		Uname:          loginUserInfo.UserName,
		Nickname:       loginUserInfo.Name,
		SeasonYear:     2019,
		LearnSeasonId:  11,
		Avatar:         loginUserInfo.Avatar,
		MenuDigit:      loginUserInfo.MenuKey,
		Watermark:      watermark,
		HasSmallFlow:   1,
		LoginType:      loginType,
		LogoutUrl:      logoutUrl,
		AppId:          appId,
	}

	return result, nil
}

// getWxAppId 获取微信AppId（返回微信类型数字，与PHP版本保持一致）
func (s userService) getWxAppId(ctx *gin.Context, assistantUid int64) int {
	if assistantUid <= 0 {
		return WxAppIdGeWechat
	}

	// 先查辅导资产（assetType=1）
	appId := s.getAppIdByAssetType(ctx, assistantUid, 1)
	if s.isValidAppId(appId) {
		return appId
	}

	// 查IMC资产（assetType=6）
	appId = s.getAppIdByAssetType(ctx, assistantUid, 6)
	if s.isValidAppId(appId) {
		return appId
	}

	// 查督学资产（assetType=4）
	appId = s.getAppIdByAssetType(ctx, assistantUid, 4)
	if s.isValidAppId(appId) {
		return appId
	}

	return WxAppIdGeWechat
}

// getAppIdByAssetType 根据资产类型获取AppId
func (s userService) getAppIdByAssetType(ctx *gin.Context, assistantUid int64, assetType int) int {
	// 获取微信类型
	wxTypeMap, err := mesh.NewClient().GetWxTypeByDeviceUids(ctx, []int64{assistantUid}, assetType)
	if err != nil {
		zlog.Warnf(ctx, "GetWxTypeByDeviceUids failed for assetType %d: %v", assetType, err)
		return 0
	}

	assistantUidStr := strconv.FormatInt(assistantUid, 10)
	wxType, exists := wxTypeMap[assistantUidStr]
	if !exists {
		return 0
	}

	return wxType
}

// isValidAppId 检查AppId是否有效
func (s userService) isValidAppId(appId int) bool {
	// 1-个微，2-企微
	return appId == WxAppIdGeWechat || appId == WxAppIdQiWechat
}

// generateWatermark 生成水印信息
func (s userService) generateWatermark(userInfo *userprofile.UserInfo) outputUser.WatermarkInfo {
	// 当前日期（YmdHi格式去掉前两位）
	currentDate := time.Now().Format("************")[2:] // 去掉前两位年份

	// 用户号码（电话号码后6位）
	userNumber := ""
	if len(userInfo.Phone) >= 6 {
		userNumber = userInfo.Phone[len(userInfo.Phone)-6:]
	} else if len(userInfo.UserPhone) >= 6 {
		userNumber = userInfo.UserPhone[len(userInfo.UserPhone)-6:]
	}

	// 用户名称（优先使用昵称，否则使用用户名）
	userName := userInfo.Name
	if userName == "" {
		userName = userInfo.UserName
	}

	return outputUser.WatermarkInfo{
		CurrentDate: currentDate,
		UserNumber:  userNumber,
		UserName:    userName,
	}
}

// getSessionInfo 获取会话信息
func (s userService) getSessionInfo(ctx *gin.Context) (int, string) {
	// 默认登录类型为 IPS
	loginType := LoginTypeIPS

	// 检查是否有 IPS 相关的 cookie
	if ips, err := ctx.Cookie("ZYBIPSCAS"); err == nil && ips != "" {
		loginType = LoginTypeIPS
	} else if passport, err := ctx.Cookie("ZYBUSS"); err == nil && passport != "" {
		loginType = LoginTypePassport
	}

	// 生成登出URL
	logoutUrl := s.generateLogoutUrl(ctx, loginType)

	return loginType, logoutUrl
}

// generateLogoutUrl 生成登出URL
func (s userService) generateLogoutUrl(ctx *gin.Context, loginType int) string {
	host := ctx.Request.Host

	// 根据登录类型生成不同的登出URL
	if loginType == LoginTypeIPS {
		// IPS 登出地址
		referer := ctx.Request.Header.Get("Referer")
		if referer == "" {
			referer = fmt.Sprintf("http://%s", host)
		}
		return fmt.Sprintf("http://%s/ips/logout?refer=%s", host, referer)
	} else {
		// Passport 登出地址
		timestamp := time.Now().Unix()
		if strings.Contains(host, "zuoyebang.cc") {
			return fmt.Sprintf("https://www.zuoyebang.cc/session/pc/logout?os=pcweb&appId=homework&channel=&plat=pc&cType=pc&fr=pc&v=%d", timestamp)
		} else {
			// 测试环境
			return fmt.Sprintf("https://%s/session/pc/logout?os=pcweb&appId=homework&channel=&plat=pc&cType=pc&fr=pc&v=%d", host, timestamp)
		}
	}
}
