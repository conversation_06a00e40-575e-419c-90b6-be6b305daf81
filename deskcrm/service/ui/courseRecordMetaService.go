package ui

import (
	"deskcrm/api/allocate"
	"deskcrm/api/coursetransgo"
	"deskcrm/api/dal"
	"deskcrm/api/dataproxy"
	"deskcrm/api/tower"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	"deskcrm/service/innerapi/trade"
	"fmt"
	"strconv"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

var CourseRecordMetaService courseRecordMetaService

type courseRecordMetaService struct {
}

func (s *courseRecordMetaService) GetCourseRecordMeta(ctx *gin.Context, req inputStudent.CourseRecordMetaParam) (*outputStudent.CourseRecordMetaResponse, error) {
	customUid := req.StudentUid
	scUid := req.PersonUid
	currentCourseId := req.CourseId
	leadsId := req.LeadsId

	// 如果 leadsId 为空，尝试获取
	if leadsId == 0 {
		leadsInfo, err := allocate.NewClient().GetLeadsByBatchCourseIdUid(ctx, []int64{currentCourseId}, customUid)
		if err != nil {
			zlog.Error(ctx, "GetLeadsByBatchCourseIdUid failed: %v", err)
			return nil, err
		}
		leadsId = leadsInfo[0].LeadsId
	}

	// 1. 获取学生的所有订单信息
	tradeList, err := trade.BillingService.GetAllTradeCourseList(ctx, customUid)
	if err != nil {
		zlog.Warnf(ctx, "getAllTradeCourseList failed: %v", err)
		return nil, fmt.Errorf("获取订单信息失败: %v", err)
	}

	// 从订单中提取课程ID集合
	courseIdSet := make([]int64, 0, len(tradeList))
	for _, tradeDetail := range tradeList {
		courseIdSet = append(courseIdSet, tradeDetail.CourseId)
	}

	if len(courseIdSet) == 0 {
		return &outputStudent.CourseRecordMetaResponse{}, nil
	}

	// 2. 获取退课调课信息（对应PHP的initCustomAllCourseRefund）
	allCourseRefund, err := s.getCustomAllCourseRefund(ctx, scUid, customUid, courseIdSet)
	if err != nil {
		zlog.Warnf(ctx, "getCustomAllCourseRefund failed: %v", err)
		// 退课信息获取失败不影响主流程，继续执行
		allCourseRefund = make(map[int64]RefundInfo)
	}

	// 3. 获取服务开始和结束时间（对应PHP的initServiceStartStopTime）
	startServiceTime, stopServiceTime, err := s.getServiceStartStopTime(ctx, leadsId, currentCourseId)
	if err != nil {
		zlog.Warnf(ctx, "getServiceStartStopTime failed: %v", err)
		// 服务时间获取失败不影响主流程，使用默认值
		startServiceTime = 0
		stopServiceTime = time.Now().Unix()
	}

	// 4. 获取课节数据（对应PHP的initLessonData）
	allAttendNum, allFinishNum, allPlayNum, err := s.getLessonData(ctx, customUid)
	if err != nil {
		zlog.Warnf(ctx, "getLessonData failed: %v", err)
		// 课节数据获取失败不影响主流程，使用默认值
		allAttendNum, allFinishNum, allPlayNum = 0, 0, 0
	}

	// 5. 计算元数据（对应PHP的initMetaData）
	metaData := s.calculateMetaData(tradeList, allCourseRefund, startServiceTime, stopServiceTime, allAttendNum, allFinishNum, allPlayNum)

	zlog.Infof(ctx, "CourseRecordMeta Execute completed, result: %+v", metaData)
	return metaData, nil
}

// RefundInfo 退课信息结构体
type RefundInfo struct {
	Refund int64 `json:"refund"`
}

// calculateMetaData 计算课程记录元数据（对应PHP的initMetaData方法）
func (s *courseRecordMetaService) calculateMetaData(
	tradeList map[int64]trade.TradeInfo,
	allCourseRefund map[int64]RefundInfo,
	startServiceTime int64,
	stopServiceTime int64,
	allAttendNum int,
	allFinishNum int,
	allPlayNum int,
) *outputStudent.CourseRecordMetaResponse {

	result := &outputStudent.CourseRecordMetaResponse{}

	// 计算总课程数
	result.TotalNum = len(tradeList)

	// 计算当前期课程数和总付费金额
	totalCurrentNum := 0
	allMoney := int64(0)

	for _, trade := range tradeList {
		// 获取退课信息
		refund := int64(0)
		if refundInfo, exists := allCourseRefund[trade.CourseId]; exists {
			refund = refundInfo.Refund
		}

		// 判断是否为当前期课程
		if refund == 0 && startServiceTime <= trade.TradeTime && stopServiceTime >= trade.TradeTime {
			totalCurrentNum++
		}

		// 累加付费金额
		allMoney += trade.TradeFee
	}

	result.TotalCurrentNum = totalCurrentNum
	result.AllMoney = allMoney

	// 设置课节统计信息
	result.AllAttendNum = allAttendNum // 已开章节
	result.AllFinishNum = allFinishNum // 到课章节
	result.AllPlayNum = allPlayNum     // 回放章节

	return result
}

// getCustomAllCourseRefund 获取学员所有课程退课+调课信息
func (s *courseRecordMetaService) getCustomAllCourseRefund(ctx *gin.Context, scUid, customUid int64, courseIds []int64) (map[int64]RefundInfo, error) {
	if scUid <= 0 || customUid <= 0 || len(courseIds) == 0 {
		return make(map[int64]RefundInfo), nil
	}

	// 调用coursetransgo API获取转课数据
	courseTransClient := coursetransgo.NewClient()
	scTransDataList, err := courseTransClient.GetCourseTransListByStaffStudent(ctx, scUid, customUid, courseIds)
	if err != nil {
		zlog.Warnf(ctx, "getCustomAllCourseRefund failed: %v", err)
		// 失败时返回空数据，不影响主流程
		return make(map[int64]RefundInfo), nil
	}

	// 转换为map结构，以courseId为key
	result := make(map[int64]RefundInfo)
	for _, item := range scTransDataList {
		result[item.CourseId] = RefundInfo{
			Refund: item.Refund,
		}
	}

	zlog.Infof(ctx, "getCustomAllCourseRefund result: %d records", len(result))
	return result, nil
}

// getServiceStartStopTime 获取服务开始和结束时间
func (s *courseRecordMetaService) getServiceStartStopTime(ctx *gin.Context, leadsId, currentCourseId int64) (int64, int64, error) {
	var startServiceTime, stopServiceTime int64

	// 1. 获取leads信息中的服务开始时间
	if leadsId > 0 {
		leadsInfo, err := allocate.NewClient().GetLeadsByIds(ctx, []int64{leadsId})
		if err != nil {
			zlog.Warnf(ctx, "GetLeadsByIds failed: %v", err)
		} else if leads, exists := leadsInfo[leadsId]; exists {
			// 使用AllocTime作为服务开始时间
			startServiceTime = leads.AllocTime
			zlog.Infof(ctx, "Found leads info for leadsId %d, allocTime: %d", leadsId, leads.AllocTime)
		} else {
			zlog.Warnf(ctx, "Leads info not found for leadsId %d", leadsId)
		}
	}

	// 2. 获取课程过期时间作为服务结束时间
	expireTime, err := tower.NewClient().GetExpireTimeByCourse(ctx, currentCourseId)
	if err != nil {
		zlog.Warnf(ctx, "GetExpireTimeByCourse failed: %v", err)
		// 如果获取失败，使用当前时间作为结束时间
		stopServiceTime = time.Now().Unix()
	} else {
		stopServiceTime = expireTime
	}

	zlog.Infof(ctx, "getServiceStartStopTime: leadsId=%d, courseId=%d, start=%d, stop=%d",
		leadsId, currentCourseId, startServiceTime, stopServiceTime)

	return startServiceTime, stopServiceTime, nil
}

// getLessonData 获取课节数据
func (s *courseRecordMetaService) getLessonData(ctx *gin.Context, customUid int64) (allAttendNum, allFinishNum, allPlayNum int, err error) {
	// 1. 获取LPC leads数据
	lpcLeadsInfo, err := s.getIdlLpcLeadsDataByStudentUids(ctx, []int64{customUid}, []string{"course_id", "playback_num", "finish_num"})
	if err != nil {
		zlog.Warnf(ctx, "getIdlLpcLeadsDataByStudentUids failed: %v", err)
		return 0, 0, 0, err
	}

	if len(lpcLeadsInfo) == 0 {
		return 0, 0, 0, nil
	}

	// 2. 提取课程ID
	courseIds := make([]int64, 0, len(lpcLeadsInfo))
	for _, info := range lpcLeadsInfo {
		courseIds = append(courseIds, info.CourseId)
	}

	// 3. 获取课程课节信息
	courseLessonInfo, err := dal.GetCourseLessonInfoByCourseIds(ctx, courseIds)
	if err != nil {
		zlog.Warnf(ctx, "GetCourseLessonInfoByCourseIds failed: %v", err)
		courseLessonInfo = make(map[string]dal.CourseInfo)
	}

	// 4. 计算已开课节数（对应PHP的classOpen逻辑）
	classOpen := make(map[int64]int)
	nowTime := time.Now().Unix()

	for courseIdStr, courseInfo := range courseLessonInfo {
		courseId, parseErr := strconv.ParseInt(courseIdStr, 10, 64)
		if parseErr != nil {
			continue
		}

		openCount := 0
		for _, lesson := range courseInfo.LessonList {
			if nowTime >= int64(lesson.StartTime) {
				openCount++
			}
		}
		classOpen[courseId] = openCount
	}

	// 计算总的已开课节数
	for _, count := range classOpen {
		allAttendNum += count
	}

	// 5. 计算已完成和回放课节数
	for _, item := range lpcLeadsInfo {
		allFinishNum += int(item.FinishNum)
		allPlayNum += int(item.PlaybackNum)
	}

	zlog.Infof(ctx, "getLessonData result: attend=%d, finish=%d, play=%d", allAttendNum, allFinishNum, allPlayNum)
	return allAttendNum, allFinishNum, allPlayNum, nil
}

// LpcLeadsInfo LPC leads数据结构体
type LpcLeadsInfo struct {
	LeadsId     int64 `json:"leads_id"`
	StudentUid  int64 `json:"student_uid"`
	CourseId    int64 `json:"course_id"`
	PlaybackNum int64 `json:"playback_num"`
	FinishNum   int64 `json:"finish_num"`
}

// getIdlLpcLeadsDataByStudentUids 获取LPC leads数据
func (s *courseRecordMetaService) getIdlLpcLeadsDataByStudentUids(ctx *gin.Context, studentUids []int64, fields []string) ([]LpcLeadsInfo, error) {
	if len(studentUids) == 0 {
		return []LpcLeadsInfo{}, nil
	}

	// 调用dataproxy API获取LPC leads数据
	dataproxyClient := dataproxy.NewClient()
	lpcLeadsDataList, err := dataproxyClient.GetIdlLpcLeadsDataByStudentUids(ctx, studentUids, fields)
	if err != nil {
		zlog.Warnf(ctx, "getIdlLpcLeadsDataByStudentUids failed: %v", err)
		// 失败时返回空数据，不影响主流程
		return []LpcLeadsInfo{}, nil
	}

	// 转换为本地结构体
	result := make([]LpcLeadsInfo, 0, len(lpcLeadsDataList))
	for _, item := range lpcLeadsDataList {
		result = append(result, LpcLeadsInfo{
			LeadsId:     item.LeadsId,
			StudentUid:  item.StudentUid,
			CourseId:    item.CourseId,
			PlaybackNum: item.PlaybackNum,
			FinishNum:   item.FinishNum,
		})
	}

	return result, nil
}
