package consts

// PerformanceV1 接口相关常量定义
// 对应 PHP Service_Page_DeskV1_Student_PerformanceV1 的常量

// Tab 类型常量 - 数据标签页类型
const (
	TAB_CORE_DATA = "core_data" // 核心数据（默认）
	TAB_EXAM      = "exam"      // 试卷数据
)

// 课程类型常量
const (
	COURSE_TYPE_TRADITIONAL = 1 // 传统课程
	COURSE_TYPE_LPC         = 2 // LPC课程
)

// 课程开始状态常量
const (
	LESSON_STATUS_NOT_STARTED = 0 // 未开课
	LESSON_STATUS_UPCOMING    = 1 // 即将开课
	LESSON_STATUS_IN_PROGRESS = 2 // 正在开课
	LESSON_STATUS_FINISHED    = 3 // 已结束
)

// 试卷绑定类型常量
const (
	BIND_TYPE_HOMEWORK = 1 // 作业绑定类型
)

// 年级类型常量（用于判断预习类型）
const (
	GRADE_TYPE_PRIMARY = 1 // 小学
	GRADE_TYPE_JUNIOR  = 2 // 初中
	GRADE_TYPE_SENIOR  = 3 // 高中
)

// 表头字段映射常量 - 对应 PHP mapHeaderFunc
const (
	// 传统课程字段
	HEADER_LESSONNAME                              = "lessonName"
	HEADER_STARTTIME                               = "startTime"
	HEADER_PREVIEW                                 = "preview"
	HEADER_ATTEND                                  = "attend"
	HEADER_PLAYBACK                                = "playback"
	HEADER_PLAYBACK_V1                             = "playbackv1"
	HEADER_LBPATTENDDURATION                       = "lbpAttendDuration"
	HEADER_LBPATTENDDURATIONOLD                    = "lbpAttendDurationOld"
	HEADER_INCLASSTEST                             = "inclassTest"
	HEADER_ORALQUESTION                            = "oralQuestion"
	HEADER_HOMEWORK                                = "homework"
	HEADER_HOMEWORK_LIKE                           = "similarHomework"
	HEADER_EXERCISE                                = "exercise"
	HEADER_EXERCISEALL                             = "exerciseAll"
	HEADER_LBPINTERACTEXAM                         = "lbpInteractExam"
	HEADER_MIX_PLAYBACK_INTERACT                   = "mixPlaybackInteract"
	HEADER_LITTLE_KID_FUDAO_HOMEWORK_STATUS        = "littleKidFudaoHomeworkStatus"
	HEADER_LITTLE_KID_FUDAO_HOMEWORK_LEVEL         = "littleKidFudaoHomeworkLevel"
	HEADER_LITTLE_KID_FUDAO_INTERACT               = "littleKidFudaoInteract"
	HEADER_SYNCHRONOUSPRACTICE                     = "synchronousPractice"
	HEADER_HASCOMPOSITIONREPORT                    = "hasCompositionReport"
	HEADER_TALK                                    = "talk"
	HEADER_SCORE                                   = "score"
	HEADER_MONTHLYEXAMREPORT                       = "monthlyExamReport"
	HEADER_IS_INCLASS_TEACHER_ROOM_ATTEND_30MINUTE = "isInclassTeacherRoomAttend30minute"
	HEADER_IS_ATTEND_FINISH                        = "isAttendFinish"
	HEADER_GJK_ATTEND_LESSON_LUBO                  = "gjkAttendLessonLubo"
	HEADER_GJK_COMPLETE_LESSON_LUBO                = "gjkCompleteLessonLubo"
	HEADER_GJK_LESSON_TAG                          = "gjkLessonTag"

	// LPC专用动态表头字段
	HEADER_LPC_LESSONNAME                  = "lpclessonName"
	HEADER_LPC_TEACHERNAME                 = "teacherName"
	HEADER_LPC_ATTENDSTATUS                = "attendStatus"
	HEADER_LPC_FINISHSTATUS                = "finishStatus"
	HEADER_LPC_PLAYSTATUS                  = "playStatus"
	HEADER_LPC_PREVIEW                     = "preView"
	HEADER_LPC_TANGTANGEXAMSTAT            = "tangtangExamStat"
	HEADER_LPC_STRENGTHPRACTICE            = "strengthPracticeStatus"
	HEADER_LPC_LESSONREPORTURL             = "lessonReportUrl"
	HEADER_DEER_ELOQUENCE_HOMEWORKLEVEL    = "deerEloquenceHomeworkLevel"
	HEADER_DEER_PROGRAMMING_HOMEWORK_LEVEL = "deerProgrammingHomeworkLevel"
	HEADER_LESSON_REPORT                   = "deerLessonReportUrl"
	HEADER_LESSON_HOMEWORK                 = "deerLessonHomeWork"
	HEADER_ZHIBO_LESSON_REPORT             = "zhiboLessonReportUrl"
)

// 错误码常量
const (
	ERROR_PARAM_INVALID     = 10001 // 参数错误
	ERROR_LEADS_NOT_FOUND   = 10002 // 获取leadsId失败
	ERROR_COURSE_NOT_FOUND  = 10003 // 课程信息不存在
	ERROR_LESSON_NOT_FOUND  = 10004 // 章节信息不存在
	ERROR_DATA_QUERY_FAILED = 10005 // 数据查询失败
)

// 数据状态颜色常量
const (
	COLOR_GREEN  = "green"  // 绿色 - 正常/完成
	COLOR_RED    = "red"    // 红色 - 异常/未完成
	COLOR_YELLOW = "yellow" // 黄色 - 警告/部分完成
	COLOR_GRAY   = "gray"   // 灰色 - 禁用/不可用
)

// 预习考试类型常量（根据年级区分）
const (
	EXAM_TYPE_PRIMARY_PREVIEW = 5  // 小学预习考试类型 (exam5)
	EXAM_TYPE_JUNIOR_PREVIEW  = 13 // 初高中预习考试类型 (exam13)
)

// LU数据字段常量
const (
	LU_FIELD_ATTEND_DURATION         = "attendDuration"          // 到课时长
	LU_FIELD_PREVIEW_TOTAL_NUM       = "previewTotalNum"         // 预习总数
	LU_FIELD_PREVIEW_PARTICIPATE_NUM = "previewParticipateNum"   // 预习参与数
	LU_FIELD_PREVIEW_CORRECT_NUM     = "previewCorrectNum"       // 预习正确数
	LU_FIELD_IN_CLASS_HANDS_UP_NUM   = "in_class_hands_up_num"   // 课中举手次数
	LU_FIELD_IN_CLASS_VIDEO_LINK_NUM = "in_class_video_link_num" // 课中连麦次数
)

// 导出相关常量
const (
	EXPORT_FORMAT_CSV   = "csv"   // CSV格式导出
	EXPORT_FORMAT_EXCEL = "excel" // Excel格式导出
)
