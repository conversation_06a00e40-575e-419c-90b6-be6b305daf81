package util

import (
	"crypto/aes"
	"crypto/md5"
	"deskcrm/api/dataproxy"
	"deskcrm/api/location"
	"encoding/hex"
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	PhoneCryptKey = "5Yc&^uEz" // 与 PHP 中的密钥保持一致
)

// maskPhone 电话号码脱敏
func MaskPhone(phone string) string {
	if len(phone) < 7 {
		return phone
	}
	// 保留前3位和后4位，中间用*替换
	return phone[:3] + "****" + phone[len(phone)-4:]
}

// getMd5Phone 获取电话号码的MD5值
func GetMd5Phone(phone string) string {
	h := md5.New()
	h.Write([]byte(phone))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// EncodePhone 使用 AES-128-ECB 加密电话号码（与 PHP 中的 Util_Encrypt::encodePhone 保持一致）
func EncodePhone(phone string) string {
	if phone == "" {
		return ""
	}

	// 处理密钥长度：PHP openssl_encrypt 对短密钥的处理方式
	key := []byte(PhoneCryptKey)
	if len(key) < 16 {
		// 零填充到16字节（与PHP openssl_encrypt行为一致）
		paddedKey := make([]byte, 16)
		copy(paddedKey, key)
		key = paddedKey
	}

	// 创建 AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return GetMd5Phone(phone) // 加密失败时回退到MD5
	}

	// 应用 PKCS#7 padding（与PHP openssl_encrypt保持一致）
	phoneBytes := []byte(phone)
	blockSize := aes.BlockSize
	padding := blockSize - len(phoneBytes)%blockSize

	// 如果数据长度正好是块大小的倍数，仍需要添加一个完整的padding块
	padText := make([]byte, padding)
	for i := range padText {
		padText[i] = byte(padding)
	}
	phoneBytes = append(phoneBytes, padText...)

	// ECB 模式加密
	encrypted := make([]byte, len(phoneBytes))
	for i := 0; i < len(phoneBytes); i += blockSize {
		block.Encrypt(encrypted[i:i+blockSize], phoneBytes[i:i+blockSize])
	}

	// 转换为十六进制字符串（小写，与PHP bin2hex保持一致）
	return hex.EncodeToString(encrypted)
}

// getPhoneLocation 获取电话号码的城市信息
func GetPhoneLocation(ctx *gin.Context, phone string) (city string, cityLevel string) {
	city = "未知"
	cityLevel = "未知"
	if phone == "" {
		return
	}

	// 调用 Location API 获取电话号码的城市信息
	locationClient := location.NewClient()
	locationResp, err := locationClient.GetPhoneLocation(ctx, phone)
	if err != nil {
		zlog.Warnf(ctx, "getPhoneLocation failed, phone: %s, err: %v", phone, err)
		return
	}

	if locationResp == nil {
		return
	}

	// 如果有城市信息，通过 DataProxy 获取城市级别
	if locationResp.City != "" {
		cityNames := []string{locationResp.City}
		dataproxyClient := dataproxy.NewClient()
		cityDataList, err := dataproxyClient.GetEsCityData(ctx, cityNames)
		if err != nil {
			zlog.Warnf(ctx, "GetEsCityData failed, city: %s, err: %v", locationResp.City, err)
			return locationResp.City, cityLevel
		}

		// 查找匹配的城市信息
		for _, cityData := range cityDataList {
			if cityData.CityName == locationResp.City {
				return cityData.CityName, cityData.CityLevelName
			}
		}
	}

	return locationResp.City, cityLevel
}
